[gd_scene load_steps=8 format=3 uid="uid://bv434clg4etl0"]

[ext_resource type="Theme" uid="uid://cek4udsfjd4md" path="res://assets/theme/cn_font_theme.theme" id="1_lahaq"]
[ext_resource type="Script" uid="uid://dlxdn1jxl6sts" path="res://view/level/level_select.gd" id="1_yjf0n"]
[ext_resource type="Texture2D" uid="uid://dihew1duuegut" path="res://view/level/images/Box_Blue_Square.png" id="2_5ows8"]
[ext_resource type="Texture2D" uid="uid://dnwfch36owiwp" path="res://view/level/images/Banner_Blue.png" id="3_tlrg5"]
[ext_resource type="Texture2D" uid="uid://cs6kstjxnlkba" path="res://view/level/images/IconButton_Small_Red_Circle.png" id="4_2w36u"]
[ext_resource type="Texture2D" uid="uid://c5812gst06fud" path="res://view/level/images/Icon_Small_Blank_Arrow.png" id="4_qvhmn"]
[ext_resource type="Texture2D" uid="uid://squnbitckjrt" path="res://view/level/images/Icon_Small_Blank_X.png" id="5_8xumt"]

[node name="LevelSelect" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("1_lahaq")
script = ExtResource("1_yjf0n")

[node name="ColorRect" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0, 0.490196, 1, 1)

[node name="MarginContainer" type="MarginContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/margin_left = 30
theme_override_constants/margin_top = 100
theme_override_constants/margin_right = 30
theme_override_constants/margin_bottom = 30

[node name="NinePatchRect" type="NinePatchRect" parent="MarginContainer"]
layout_mode = 2
texture = ExtResource("2_5ows8")
patch_margin_left = 200
patch_margin_top = 200
patch_margin_right = 200
patch_margin_bottom = 200

[node name="MarginContainer" type="MarginContainer" parent="MarginContainer/NinePatchRect"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/margin_left = 60
theme_override_constants/margin_top = 60
theme_override_constants/margin_right = 60
theme_override_constants/margin_bottom = 60

[node name="ScrollContainer" type="ScrollContainer" parent="MarginContainer/NinePatchRect/MarginContainer"]
layout_mode = 2
horizontal_scroll_mode = 0

[node name="MarginContainer" type="MarginContainer" parent="MarginContainer/NinePatchRect/MarginContainer/ScrollContainer"]
layout_mode = 2
theme_override_constants/margin_left = 20
theme_override_constants/margin_top = 100
theme_override_constants/margin_right = 20
theme_override_constants/margin_bottom = 30

[node name="GridContainer" type="GridContainer" parent="MarginContainer/NinePatchRect/MarginContainer/ScrollContainer/MarginContainer"]
layout_mode = 2
theme_override_constants/h_separation = 46
theme_override_constants/v_separation = 26
columns = 10

[node name="TextureRect" type="TextureRect" parent="MarginContainer/NinePatchRect"]
layout_mode = 2
offset_left = 655.0
offset_top = -100.0
offset_right = 1265.0
offset_bottom = 145.0
grow_horizontal = 2
texture = ExtResource("3_tlrg5")
expand_mode = 5
stretch_mode = 4

[node name="Label" type="Label" parent="MarginContainer/NinePatchRect/TextureRect"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_bottom = -40.0
grow_horizontal = 2
grow_vertical = 2
theme_override_font_sizes/font_size = 80
text = "关卡选择"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Back" type="TextureButton" parent="MarginContainer/NinePatchRect/TextureRect"]
layout_mode = 0
offset_left = -118.0
offset_top = 49.0
offset_right = 50.0
offset_bottom = 203.0
texture_normal = ExtResource("4_qvhmn")
flip_h = true

[node name="Next" type="TextureButton" parent="MarginContainer/NinePatchRect/TextureRect"]
layout_mode = 0
offset_left = 560.0
offset_top = 45.0
offset_right = 728.0
offset_bottom = 199.0
texture_normal = ExtResource("4_qvhmn")

[node name="Close" type="TextureButton" parent="MarginContainer/NinePatchRect"]
layout_mode = 0
offset_left = 1750.0
offset_top = -40.0
offset_right = 2017.0
offset_bottom = 227.0
scale = Vector2(0.5, 0.5)
texture_normal = ExtResource("4_2w36u")
metadata/_edit_group_ = true

[node name="CenterContainer" type="CenterContainer" parent="MarginContainer/NinePatchRect/Close"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="TextureRect" type="TextureRect" parent="MarginContainer/NinePatchRect/Close/CenterContainer"]
layout_mode = 2
texture = ExtResource("5_8xumt")

[connection signal="tree_exiting" from="." to="." method="_on_tree_exiting"]
[connection signal="pressed" from="MarginContainer/NinePatchRect/TextureRect/Back" to="." method="_on_back_map_pressed"]
[connection signal="pressed" from="MarginContainer/NinePatchRect/TextureRect/Next" to="." method="_on_next_map_pressed"]
[connection signal="pressed" from="MarginContainer/NinePatchRect/Close" to="." method="_on_close_pressed"]
