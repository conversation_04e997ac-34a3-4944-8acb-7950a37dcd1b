[gd_scene load_steps=7 format=3 uid="uid://disp5ifq7freh"]

[ext_resource type="Script" uid="uid://d1e74yaubudu4" path="res://view/level/level_button.gd" id="1_dcte5"]
[ext_resource type="Texture2D" uid="uid://dloiuv1c1hhvh" path="res://view/level/images/IconButton_Large_Orange_Square.png" id="2_mj0lx"]
[ext_resource type="PackedScene" uid="uid://d2mfitkmgfb5d" path="res://view/level/level_star.tscn" id="3_2okiv"]
[ext_resource type="Texture2D" uid="uid://jlu8sybhb6rx" path="res://view/level/images/Icon_Small_Lock.png" id="4_0jkyl"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_4wlgn"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_v44kg"]

[node name="LevelButton" type="Control"]
custom_minimum_size = Vector2(128, 128)
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_right = -1792.0
offset_bottom = -952.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_dcte5")

[node name="TextureRect" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("2_mj0lx")
expand_mode = 4
stretch_mode = 4

[node name="Label" type="Label" parent="."]
visible = false
layout_mode = 1
anchors_preset = 10
anchor_right = 1.0
offset_bottom = 110.0
grow_horizontal = 2
theme_override_constants/outline_size = 15
theme_override_font_sizes/font_size = 80
text = "0"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ReferenceRect" type="ReferenceRect" parent="."]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
border_color = Color(1, 1, 1, 1)
border_width = 2.0
editor_only = false

[node name="HBoxContainer" type="HBoxContainer" parent="."]
visible = false
layout_mode = 1
anchors_preset = -1
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_bottom = 10.0
grow_horizontal = 2
grow_vertical = 0

[node name="LevelStar" parent="HBoxContainer" instance=ExtResource("3_2okiv")]
layout_mode = 2

[node name="LevelStar2" parent="HBoxContainer" instance=ExtResource("3_2okiv")]
custom_minimum_size = Vector2(56, 56)
layout_mode = 2

[node name="LevelStar3" parent="HBoxContainer" instance=ExtResource("3_2okiv")]
layout_mode = 2

[node name="TextureRect2" type="TextureRect" parent="."]
layout_mode = 0
offset_left = 30.0
offset_top = 20.0
offset_right = 98.0
offset_bottom = 108.0
texture = ExtResource("4_0jkyl")
expand_mode = 4
stretch_mode = 4

[node name="Button" type="Button" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/pressed = SubResource("StyleBoxEmpty_4wlgn")
theme_override_styles/normal = SubResource("StyleBoxEmpty_v44kg")
flat = true

[connection signal="mouse_entered" from="Button" to="." method="_on_mouse_entered"]
[connection signal="mouse_exited" from="Button" to="." method="_on_mouse_exited"]
[connection signal="pressed" from="Button" to="." method="_on_button_pressed"]
