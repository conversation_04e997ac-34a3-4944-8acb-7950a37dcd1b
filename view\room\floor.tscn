[gd_scene load_steps=22 format=4 uid="uid://dygetii6f3uhy"]

[ext_resource type="Texture2D" uid="uid://dg3xeuxadmk8t" path="res://assets/Dungeon/gltf/dungeon_texture.png" id="1_mku4j"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_4xxdk"]
resource_name = "texture"
albedo_texture = ExtResource("1_mku4j")
roughness = 0.45

[sub_resource type="ArrayMesh" id="ArrayMesh_063lp"]
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.15, 2),
"format": 34359742465,
"index_count": 198,
"index_data": PackedByteArray("GAADABoAGwACAB0AHgABABQABgANABIABgAJAA0ABgALAAkABgAHAAsABwAIAAsABwAEAAgADAALAAoADAAPAAsADwAJAAsADwANAAkAEgAHAAYAEgATAAcAEwAFAAcAEwAQAAUAEQAPAA4AEQATAA8AEwANAA8AEwASAA0AFAAHAAUAFAAWAAcAFgAEAAcAFgAVAAQACgAZABgACgALABkACwAXABkACwAIABcADgAcABsADgAPABwADwAaABwADwAMABoAHQATABEAHQAfABMAHwAQABMAHwAeABAAFQAAABcAHQAiAB8AHQACACIAGgAjABwAGgADACMAHwAiACEAGwAiAAIAGwAcACIAFwAgABkAFwAAACAAHAAjACIAGQADABgAGQAjAAMAFAAhABYAFAABACEAFgAAABUAFgAgAAAAHwABAB4AHwAhAAEAGQAgACMABAAXAAgABAAVABcACgAaAAwACgAYABoADgAdABEADgAbAB0AEAAUAAUAEAAeABQAFgAhACAA"),
"name": "texture",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 36,
"vertex_data": PackedByteArray("AACAP87MTD0AAIC/AACAv87MTD0AAIC/AACAv87MTD0AAIA/AACAP87MTD0AAIA/zMxMPc7MTD3Ryiq/zMxMvc7MTD3Ryiq/AAAAAM7MTD1uYxO/AACAMA3XozzkBiW/0coqP87MTD3MzEy9bmMTP87MTD0AAACA0coqP87MTD3MzEw94wYlPw3XozwAAIAwzMxMPc7MTD3Ryio/AAAAAM7MTD1uYxM/zMxMvc7MTD3Ryio/AACAsA3XozzjBiU/0coqv87MTD3MzEy90coqv87MTD3MzEw9bmMTv87MTD0AAACA5AYlvw3XozwAAICwzMxMvc7MTD0AAIC/zMxMPc7MTD0AAIC/AAAAAA3XozwAAIC/AACAP87MTD3MzEy9AACAP87MTD3MzEw9AACAPw3XozwAAACAzMxMPc7MTD0AAIA/zMxMvc7MTD0AAIA/AAAAAA3XozwAAIA/AACAv87MTD3MzEw9AACAv87MTD3MzEy9AACAvw3XozwAAACAAACAP87MzL0AAIC/AACAv87MzL0AAIC/AACAv87MzL0AAIA/AACAP87MzL0AAIA/")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_y823x"]
resource_name = "floor_tile_small_floor_tile_small"
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.15, 2),
"attribute_data": PackedByteArray("Me44PyiSBD4x7jg/uEElPjHuOD+4QSU+zxEiPyiSBD7PESI/uEElPs8RIj+4QSU+zxEiPyiSBD7PESI/uEElPs8RIj+4QSU+Me44PyiSBD4x7jg/uEElPjHuOD+4QSU+RhIuP1AkyT1GEi4/UCTpPUYSLj8okgQ+uu0sP1AkyT267Sw/KJIEPrrtLD8okhQ+AIAtPyiSBD4AgC0/KJIUPgCALT9M8QU+AIAtP0zxBT4AgC0/TPEVPgCALT9M8SU+AIAtP0zxNT4rIDU/UCTJPSsgNT8okgQ+wRQ0PyiSBD7BFDQ/KJIUPisgNT8okgQ+KyA1PyiSJD5J3jQ/TPEFPkneND9M8SU+Sd40P0zxRT5GEi4/UCTpPUYSLj8okgQ+RhIuPyiSJD4AgC0/KJIEPgCALT8okhQ+uu0sPyiSBD667Sw/KJIUPrrtLD8okiQ+AIAtP0zxFT4AgC0/TPElPgCALT9M8TU+AIAtP0zxRT4AgC0/TPFFPtXfJT9QJMk91d8lPyiSBD7V3yU/KJIEPtXfJT8okiQ+P+smPyiSBD4/6yY/KJIUPrghJj9M8QU+uCEmP0zxJT64ISY/TPFFPrrtLD8okgQ+uu0sP7hBFT667Sw/uEElPkYSLj8okgQ+RhIuP7hBFT5GEi4/uEElPgCALT+4QRU+AIAtP7hBFT4AgC0/uEElPjHuOD8okgQ+Me44P7hBFT4x7jg/uEElPjHuOD8okgQ+Me44P7hBFT4x7jg/uEElPjHuOD+4QRU+Me44P7hBFT4x7jg/uEElPkYSLj8okgQ+RhIuP7hBFT5GEi4/uEElPrrtLD8okgQ+uu0sP7hBFT667Sw/uEElPgCALT+4QRU+AIAtP7hBFT4AgC0/uEElPs8RIj8okgQ+zxEiP7hBFT7PESI/uEElPs8RIj8okgQ+zxEiP7hBFT7PESI/uEElPs8RIj+4QRU+zxEiP7hBFT7PESI/uEElPjHuOD+4QSU+Me44P7hBJT7PESI/uEElPs8RIj+4QSU+zxEiP7hBJT7PESI/uEElPjHuOD+4QSU+Me44P7hBJT4="),
"format": 34359742487,
"index_count": 198,
"index_data": PackedByteArray("RAAJAEoATQAGAFMAVgADADgAEgAlADMAEgAbACUAEwAgABwAEwAXACAAFAAZAB8AFAAMABkAJAAhAB4AJAAtACEAKwAcACAAKwAmABwANAAXABMANAA2ABcANQAPABUANQAvAA8AMgAuACkAMgA3AC4ANgAmACsANgA0ACYAOQAYABEAOQA/ABgAPgANABYAPgA8AA0AHgBIAEUAHgAhAEgAHwBCAEcAHwAZAEIAKABRAE4AKAAsAFEAKgBLAFAAKgAiAEsAVAA3ADIAVABaADcAWQAvADUAWQBXAC8AOwAAAEEAVQBgAFsAVQAHAGAATABiAFIATAAKAGIAWwBgAF4ATwBhAAgATwBSAGEAQwBdAEkAQwACAF0AUgBiAGEASQALAEYASQBjAAsAOgBfAEAAOgAFAF8AQAABAD0AQABcAAEAWwAEAFgAWwBeAAQASQBdAGMADgBBABoADgA7AEEAHQBKACMAHQBEAEoAJwBTADEAJwBNAFMAMAA4ABAAMABWADgAQABfAFwA"),
"material": SubResource("StandardMaterial3D_4xxdk"),
"name": "texture",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 100,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_063lp")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_6giyx"]
data = PackedVector3Array(1, 0.05, 0.05, 1, 0.05, 1, 0.05, 0.05, 1, -0.05, 0.05, 1, -1, 0.05, 1, -1, 0.05, 0.05, -1, 0.05, -0.05, -1, 0.05, -1, -0.05, 0.05, -1, 0, 0.05, -0.5757, 0, 0.05, 0.5757, -0.5757, 0.05, 0, 0, 0.05, -0.5757, 0.5757, 0.05, 0, 0, 0.05, 0.5757, 0, 0.05, -0.5757, 0.6446, 0.02, 0, 0.5757, 0.05, 0, 0, 0.05, -0.5757, 0, 0.02, -0.6446, 0.6446, 0.02, 0, 0, 0.02, -0.6446, 0.6672, 0.05, -0.05, 0.6446, 0.02, 0, 0, 0.02, -0.6446, 0.05, 0.05, -0.6672, 0.6672, 0.05, -0.05, 0.05, 0.05, 0.6672, 0.6446, 0.02, 0, 0.6672, 0.05, 0.05, 0.05, 0.05, 0.6672, 0, 0.02, 0.6446, 0.6446, 0.02, 0, 0, 0.02, 0.6446, 0.5757, 0.05, 0, 0.6446, 0.02, 0, 0, 0.02, 0.6446, 0, 0.05, 0.5757, 0.5757, 0.05, 0, -0.5757, 0.05, 0, 0, 0.02, -0.6446, 0, 0.05, -0.5757, -0.5757, 0.05, 0, -0.6446, 0.02, 0, 0, 0.02, -0.6446, -0.6446, 0.02, 0, -0.05, 0.05, -0.6672, 0, 0.02, -0.6446, -0.6446, 0.02, 0, -0.6672, 0.05, -0.05, -0.05, 0.05, -0.6672, -0.6672, 0.05, 0.05, 0, 0.02, 0.6446, -0.05, 0.05, 0.6672, -0.6672, 0.05, 0.05, -0.6446, 0.02, 0, 0, 0.02, 0.6446, -0.6446, 0.02, 0, 0, 0.05, 0.5757, 0, 0.02, 0.6446, -0.6446, 0.02, 0, -0.5757, 0.05, 0, 0, 0.05, 0.5757, -0.05, 0.05, -1, 0, 0.02, -0.6446, -0.05, 0.05, -0.6672, -0.05, 0.05, -1, 0, 0.02, -1, 0, 0.02, -0.6446, 0, 0.02, -1, 0.05, 0.05, -0.6672, 0, 0.02, -0.6446, 0, 0.02, -1, 0.05, 0.05, -1, 0.05, 0.05, -0.6672, 0.6672, 0.05, 0.05, 1, 0.02, 0, 1, 0.05, 0.05, 0.6672, 0.05, 0.05, 0.6446, 0.02, 0, 1, 0.02, 0, 0.6446, 0.02, 0, 1, 0.05, -0.05, 1, 0.02, 0, 0.6446, 0.02, 0, 0.6672, 0.05, -0.05, 1, 0.05, -0.05, -0.05, 0.05, 0.6672, 0, 0.02, 1, -0.05, 0.05, 1, -0.05, 0.05, 0.6672, 0, 0.02, 0.6446, 0, 0.02, 1, 0, 0.02, 0.6446, 0.05, 0.05, 1, 0, 0.02, 1, 0, 0.02, 0.6446, 0.05, 0.05, 0.6672, 0.05, 0.05, 1, -1, 0.05, 0.05, -0.6446, 0.02, 0, -0.6672, 0.05, 0.05, -1, 0.05, 0.05, -1, 0.02, 0, -0.6446, 0.02, 0, -1, 0.02, 0, -0.6672, 0.05, -0.05, -0.6446, 0.02, 0, -1, 0.02, 0, -1, 0.05, -0.05, -0.6672, 0.05, -0.05, 0.05, 0.05, -1, 1, 0.05, -1, 1, 0.05, -0.05, -1, 0.05, 0.05, -1, -0.1, 1, -1, 0.02, 0, -1, 0.05, 0.05, -1, 0.05, 1, -1, -0.1, 1, 0.05, 0.05, 1, 1, -0.1, 1, 0, 0.02, 1, 0.05, 0.05, 1, 1, 0.05, 1, 1, -0.1, 1, -1, 0.02, 0, -1, -0.1, 1, -1, -0.1, -1, -0.05, 0.05, 1, -1, -0.1, 1, -1, 0.05, 1, -0.05, 0.05, 1, 0, 0.02, 1, -1, -0.1, 1, 1, 0.05, -0.05, 1, -0.1, -1, 1, 0.02, 0, 1, 0.05, -0.05, 1, 0.05, -1, 1, -0.1, -1, 0, 0.02, 1, 1, -0.1, 1, -1, -0.1, 1, 1, 0.02, 0, 1, 0.05, 1, 1, 0.05, 0.05, 1, 0.02, 0, 1, -0.1, 1, 1, 0.05, 1, -0.05, 0.05, -1, -1, -0.1, -1, 0, 0.02, -1, -0.05, 0.05, -1, -1, 0.05, -1, -1, -0.1, -1, 0, 0.02, -1, 1, 0.05, -1, 0.05, 0.05, -1, 0, 0.02, -1, 1, -0.1, -1, 1, 0.05, -1, -1, 0.02, 0, -1, 0.05, -1, -1, 0.05, -0.05, -1, 0.02, 0, -1, -0.1, -1, -1, 0.05, -1, 1, 0.02, 0, 1, -0.1, -1, 1, -0.1, 1, 0.05, 0.05, -0.6672, 1, 0.05, -0.05, 0.6672, 0.05, -0.05, 0.05, 0.05, -0.6672, 0.05, 0.05, -1, 1, 0.05, -0.05, 0.6672, 0.05, 0.05, 0.05, 0.05, 1, 0.05, 0.05, 0.6672, 0.6672, 0.05, 0.05, 1, 0.05, 0.05, 0.05, 0.05, 1, -0.05, 0.05, 0.6672, -1, 0.05, 0.05, -0.6672, 0.05, 0.05, -0.05, 0.05, 0.6672, -0.05, 0.05, 1, -1, 0.05, 0.05, -0.6672, 0.05, -0.05, -0.05, 0.05, -1, -0.05, 0.05, -0.6672, -0.6672, 0.05, -0.05, -1, 0.05, -0.05, -0.05, 0.05, -1, 0, 0.02, -1, -1, -0.1, -1, 1, -0.1, -1)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_ii3vs"]
resource_name = "texture"
albedo_texture = ExtResource("1_mku4j")
roughness = 0.45

[sub_resource type="ArrayMesh" id="ArrayMesh_ea6vo"]
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.15, 2),
"format": 34359742465,
"index_count": 282,
"index_data": PackedByteArray("JQADABYAAgAiABcAJgABABAAIwAHAAkAIwAhAAcAJQAJAAgAJQAkAAkAKgAuADEAKgAtAC4AJwAFAAYAJwAmAAUAIgAMAAsAIgAgAAwAEAAGAAUAEAASAAYAEgAEAAYAEgARAAQACAAVABQACAAJABUACQATABUACQAHABMACwAYABcACwAMABgADAAWABgADAAKABYAGQAPAA4AGQAbAA8AGwANAA8AGwAaAA0AIQAAABMAGQAeABsAGQACAB4AFgAfABgAFgADAB8AGwAeAB0AFwAeAAIAFwAYAB4AEwAcABUAEwAAABwAGAAfAB4AFQADABQAFQAfAAMAEAAdABIAEAABAB0AEgAAABEAEgAcAAAAGwABABoAGwAdAAEAFQAcAB8AIQATAAcAJQAWAAoAIgAZAA4AJgAQAAUAEgAdABwAFAADACUABAARACEADgAgACIADgAPACAABgAqACcABgAhACMABgAEACEADAAgADAADQAaACYACAAUACUADwAmACcADwANACYACQApACMACgAkACUACgAMACQAGgABACYACwAXACIAEQAAACEAGQAiAAIAKgAPACcAKQAtACoAKQAsAC0ALQAvAC4ALwAsACsALwAtACwAKAAsACkAKAArACwAMAAgADEAMAAuAC8AMAAxAC4AMAArACgAMAAvACsAIAAPADEAJAAwACgAJAAMADAACQAoACkAJAAoAAkABgApACoAIwApAAYAMQAPACoA"),
"name": "texture",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 50,
"vertex_data": PackedByteArray("AACAP87MTD0AAIC/AACAv87MTD0AAIC/AACAv87MTD0AAIA/AACAP87MTD0AAIA/zMxMPc7MTD1rZES/zMxMvc7MTD1rZES/AACAMA3Xozx+oD6/OLc2P87MTD3MzEy9OLc2P87MTD3MzEw92bUmPw3XozwAAIAwzMxMPc7MTD1Rczw/zMxMvc7MTD1Rczw/AACAsA3XozxjrzY/a2REv87MTD3MzEy9a2REv87MTD3MzEw9fqA+vw3XozwAAICwzMxMvc7MTD0AAIC/zMxMPc7MTD0AAIC/AAAAAA3XozwAAIC/AACAP87MTD3MzEy9AACAP87MTD3MzEw9AACAPw3XozwAAACAzMxMPc7MTD0AAIA/zMxMvc7MTD0AAIA/AAAAAA3XozwAAIA/AACAv87MTD3MzEw9AACAv87MTD3MzEy9AACAvw3XozwAAACAAACAP87MzL0AAIC/AACAv87MzL0AAIC/AACAv87MzL0AAIA/AACAP87MzL0AAIA/uLbyvg3XozyC4uk+v3EOP87MTD1bIB2/uaMCv87MTD08c/w+EWcCPw3XozzibQ+/FzrYPg3XozyltgU/0crqPs7MTD0C/w4/VlzXvs7MTD0Udjy//Ea/vg3Xozyq+DC/1AO3Pg3XozytsYA+wACLPg3XozzhA06+QJgNvg3XozwjZau+qX6SPs7MTD2AXlc+mrpuPs7MTD1acyu+RQ+cvc7MTD0EQYW+jrqCvs7MTD0OXMI9BjwyPczMTD1QxJk+2o0DPQ3XozzOK8A+RJemvg3XozxybgE+")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_cykgr"]
resource_name = "floor_tile_small_broken_A_floor_tile_small_broken_A"
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.15, 2),
"attribute_data": PackedByteArray("Me44PyiSBD4x7jg/uEElPjHuOD+4QSU+zxEiPyiSBD7PESI/uEElPs8RIj+4QSU+zxEiPyiSBD7PESI/uEElPs8RIj+4QSU+Me44PyiSBD4x7jg/uEElPjHuOD+4QSU+RhIuP1AkyT1GEi4/UCTpPUYSLj8okgQ+uu0sP1AkyT267Sw/KJIEPrrtLD8okhQ+AIAtP0zxBT4AgC0/TPEFPgCALT9M8RU+AIAtP0zxJT4AgC0/TPE1PisgNT9QJMk9KyA1PyiSBD4rIDU/KJIEPisgNT8okiQ+Sd40P0zxBT5J3jQ/TPElPkneND9M8UU+RhIuP1Ak6T1GEi4/KJIEPkYSLj8okiQ+uu0sPyiSBD667Sw/KJIUPrrtLD8okiQ+AIAtP0zxFT4AgC0/TPElPgCALT9M8TU+AIAtP0zxRT4AgC0/TPFFPtXfJT9QJMk91d8lPyiSBD7V3yU/KJIEPtXfJT8okiQ+uCEmP0zxBT64ISY/TPElPrghJj9M8UU+uu0sP7hBBT667Sw/uEEVPrrtLD+4QSU+RhIuP7hBBT5GEi4/uEEVPkYSLj+4QSU+AIAtP7hBFT4AgC0/uEEVPgCALT+4QSU+Me44P7hBBT4x7jg/uEEVPjHuOD+4QSU+Me44P7hBBT4x7jg/uEEVPjHuOD+4QSU+Me44P7hBFT4x7jg/uEEVPjHuOD+4QSU+RhIuP7hBBT5GEi4/uEEVPkYSLj+4QSU+uu0sP7hBBT667Sw/uEEVPrrtLD+4QSU+AIAtP7hBFT4AgC0/uEEVPgCALT+4QSU+zxEiP7hBBT7PESI/uEEVPs8RIj+4QSU+zxEiP7hBBT7PESI/uEEVPs8RIj+4QSU+zxEiP7hBFT7PESI/uEEVPs8RIj+4QSU+Me44P7hBJT4x7jg/uEElPs8RIj+4QSU+zxEiP7hBJT7PESI/uEElPs8RIj+4QSU+Me44P7hBJT4x7jg/uEElPtzQKT9M8SU+3NApP0zxRT6i3Ss/TPElPjiZMT9QJMk9OJkxPyiSBD48gDM/KJIEPshmKT8okgQ+yGYpPyiSJD4kLzE/TPEFPiQvMT9M8SU+JC8xP0zxJT4kLzE/TPFFPjiZMT8okgQ+OJkxPyiSJD48gDM/KJIEPsR/Jz8okgQ+yGYpP1AkyT3IZik/KJIEPtzQKT9M8QU+3NApP0zxJT5EyC4/TPElPkTILj9M8UU+RcguP0jxJT5FyC4/SPFFPvv+Lj9M8SU++/4uP0zxRT78/i4/SPElPvz+Lj9I8UU++YAsP0jxJT75gCw/TPElPvmALD9I8UU++YAsP0zxRT5EyC4/TPEDPkTILj9M8SU+RcguP0jxJT77/i4/TPEDPvv+Lj9M8SU+/P4uP0jxJT75gCw/TPEDPvmALD9I8SU++YAsP0zxJT4eXCw/UPEDPh5cLD9M8SU+hkQtP0zxAz6IRC0/UPElPsASLT9M8SU+wBItP0jxRT5lJy0/SPElPj9YLD9M8SU+P1gsP0jxRT4="),
"format": 34359742487,
"index_count": 282,
"index_data": PackedByteArray("agAJAEIABgBiAEUAawADADAAZAAXABsAZABfABcAaQAdABoAaQBnAB0AegCGAI0AegCDAIYAbgAPABMAbgBsAA8AYwAoACMAYwBdACgAMQAWABEAMQA3ABYANgANABQANgA0AA0AGgBAAD0AGgAdAEAAGwA6AD8AGwAXADoAIgBJAEYAIgAmAEkAJABDAEgAJAAeAEMATAAvACwATABSAC8AUQApAC0AUQBPACkAYQAAADkATQBYAFMATQAHAFgARABaAEoARAAKAFoAUwBYAFYARwBZAAgARwBKAFkAOwBVAEEAOwACAFUASgBaAFkAQQALAD4AQQBbAAsAMgBXADgAMgAFAFcAOAABADUAOABUAAEAUwAEAFAAUwBWAAQAQQBVAFsAYAA5ABgAaABCAB8AYgBLACsAbQAwABAAOABXAFQAPAAJAGoADgAzAGEALABdAGMALAAvAF0AFQB5AG8AEgBfAGQAEgAMAF8AJQBcAIkAKgBOAGsAGQA8AGoALQBsAG4ALQApAGwAHAB0AGUAIABnAGkAIAAnAGcATgADAGsAIQBFAGIAMwAAAGEASwBiAAYAeAAuAG8AdwCEAHsAdwCBAIQAggCHAIUAhwB/AHwAhwCCAH8AcwCAAHUAcwB+AIAAiwBeAIwAigCGAIgAigCNAIYAigB9AHEAigCIAH0AXgAuAIwAZgCJAHAAZgAlAIkAHAByAHQAZgByABwAFQB2AHkAZQB2ABUAjAAuAHgA"),
"material": SubResource("StandardMaterial3D_ii3vs"),
"name": "texture",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 142,
"vertex_data": PackedByteArray("AACAP87MTD0AAIC/AACAP87MTD0AAIC/AACAP87MTD0AAIC/AACAv87MTD0AAIC/AACAv87MTD0AAIC/AACAv87MTD0AAIC/AACAv87MTD0AAIA/AACAv87MTD0AAIA/AACAv87MTD0AAIA/AACAP87MTD0AAIA/AACAP87MTD0AAIA/AACAP87MTD0AAIA/zMxMPc7MTD1rZES/zMxMPc7MTD1rZES/zMxMPc7MTD1rZES/zMxMvc7MTD1rZES/zMxMvc7MTD1rZES/zMxMvc7MTD1rZES/AACAMA3Xozx+oD6/AACAMA3Xozx+oD6/AACAMA3Xozx+oD6/AACAMA3Xozx+oD6/AACAMA3Xozx+oD6/OLc2P87MTD3MzEy9OLc2P87MTD3MzEy9OLc2P87MTD3MzEw9OLc2P87MTD3MzEw92bUmPw3XozwAAIAw2bUmPw3XozwAAIAw2bUmPw3XozwAAIAwzMxMPc7MTD1Rczw/zMxMPc7MTD1Rczw/zMxMPc7MTD1Rczw/zMxMvc7MTD1Rczw/zMxMvc7MTD1Rczw/zMxMvc7MTD1Rczw/AACAsA3XozxjrzY/AACAsA3XozxjrzY/AACAsA3XozxjrzY/AACAsA3XozxjrzY/AACAsA3XozxjrzY/a2REv87MTD3MzEy9a2REv87MTD3MzEy9a2REv87MTD3MzEw9a2REv87MTD3MzEw9fqA+vw3XozwAAICwfqA+vw3XozwAAICwfqA+vw3XozwAAICwzMxMvc7MTD0AAIC/zMxMvc7MTD0AAIC/zMxMvc7MTD0AAIC/zMxMPc7MTD0AAIC/zMxMPc7MTD0AAIC/zMxMPc7MTD0AAIC/AAAAAA3XozwAAIC/AAAAAA3XozwAAIC/AAAAAA3XozwAAIC/AACAP87MTD3MzEy9AACAP87MTD3MzEy9AACAP87MTD3MzEy9AACAP87MTD3MzEw9AACAP87MTD3MzEw9AACAP87MTD3MzEw9AACAPw3XozwAAACAAACAPw3XozwAAACAAACAPw3XozwAAACAzMxMPc7MTD0AAIA/zMxMPc7MTD0AAIA/zMxMPc7MTD0AAIA/zMxMvc7MTD0AAIA/zMxMvc7MTD0AAIA/zMxMvc7MTD0AAIA/AAAAAA3XozwAAIA/AAAAAA3XozwAAIA/AAAAAA3XozwAAIA/AACAv87MTD3MzEw9AACAv87MTD3MzEw9AACAv87MTD3MzEw9AACAv87MTD3MzEy9AACAv87MTD3MzEy9AACAv87MTD3MzEy9AACAvw3XozwAAACAAACAvw3XozwAAACAAACAvw3XozwAAACAAACAP87MzL0AAIC/AACAP87MzL0AAIC/AACAv87MzL0AAIC/AACAv87MzL0AAIC/AACAv87MzL0AAIA/AACAv87MzL0AAIA/AACAP87MzL0AAIA/AACAP87MzL0AAIA/uLbyvg3XozyC4uk+uLbyvg3XozyC4uk+uLbyvg3XozyC4uk+v3EOP87MTD1bIB2/v3EOP87MTD1bIB2/v3EOP87MTD1bIB2/uaMCv87MTD08c/w+uaMCv87MTD08c/w+EWcCPw3XozzibQ+/EWcCPw3XozzibQ+/FzrYPg3XozyltgU/FzrYPg3XozyltgU/0crqPs7MTD0C/w4/0crqPs7MTD0C/w4/0crqPs7MTD0C/w4/VlzXvs7MTD0Udjy/VlzXvs7MTD0Udjy/VlzXvs7MTD0Udjy//Ea/vg3Xozyq+DC//Ea/vg3Xozyq+DC/1AO3Pg3XozytsYA+1AO3Pg3XozytsYA+1AO3Pg3XozytsYA+1AO3Pg3XozytsYA+wACLPg3XozzhA06+wACLPg3XozzhA06+wACLPg3XozzhA06+wACLPg3XozzhA06+QJgNvg3XozwjZau+QJgNvg3XozwjZau+QJgNvg3XozwjZau+QJgNvg3XozwjZau+qX6SPs7MTD2AXlc+qX6SPs7MTD2AXlc+qX6SPs7MTD2AXlc+mrpuPs7MTD1acyu+mrpuPs7MTD1acyu+mrpuPs7MTD1acyu+RQ+cvc7MTD0EQYW+RQ+cvc7MTD0EQYW+RQ+cvc7MTD0EQYW+jrqCvs7MTD0OXMI9jrqCvs7MTD0OXMI9BjwyPczMTD1QxJk+BjwyPczMTD1QxJk+2o0DPQ3XozzOK8A+2o0DPQ3XozzOK8A+2o0DPQ3XozzOK8A+RJemvg3XozxybgE+RJemvg3XozxybgE+/3//////yV3/////////v////3////+//3////LX/78AAP9/////v/////////+//3//////ch8AAP9/////v/9//3////8//3///4LG/z//f/9/////P////3////+/a2NozCzNsslrY2jMZd1mLP9///894v8/4Je+yWPsqqv/f/////8PO+CXvskZdhZQaF8mzRrJt8tfn7DLkOT3p2hfJs3fiB22/3//////hCpfn7DLbtdorYtvE9G71MDA/3///+ub/z//f/////+d6khPDPEC9w9ewGl00be5/r3/f/////9/k/BO4On1+kliBEmf4AuH6s//f/////+crARJn+Dq7icj/3///8PT/7+ctivf+JtfOZy2K9/rwmRIIEr73RyIIc7/f///////v861U92Cm846IEr73cXrxiHOtVPd+cCAST+UzsuE/1PZ/3//////eRX/f///+K7/v2y2Iusxyww/rJFWzB3+3ar/f///WOL/v8W1P+3pyuo9/3//////Dzv/r//P/8//p/////////+//3//////Qjn/T//P/8//J/////////+//0//z//P/yf/r//P/8//p/////////+//3///+ub/z//f//PqfQcz////3////+//3//////ner/r///////v////3////+//3//z////z//r///B/H0U////3////+//3//////nKz/T//PNJEoxf9//3////8//3///////cr/r//P/8//p/9//3////8//0//z//P/yf/r//PSpJ8Rf9//3////8//3//////ch//r///3u2ZPAAA/3////+//3////LX/7//f//P////PwAA/3////+//3//z3XwFb3/r///////vwAA/3////+//////////7////9/////vwAA/3////+//////////78AAP9/////v/9//3////8//3//f////z////9/////v/9///////+/srvc4hLB3D//f/////8jbDRoms0+v/O+/3///+ub/z//f//////GWP9//////3Ifsbuu4rHA3T/zZhfOaLvxvv9////63f8//3//////f5PbSAvmu/02YP9//////5ys8Ujt5eP9QWD/f/////9hlf9////y1/+/M5gYx5T+gtz/f/////8PO0GUqsZw/hLY/3//////Whb/f///////v2ydotMn7Oik/3//////f5NsnaLTjhQJ/P9///+q0/8/SbUk5HsTPYz/f/////+PMEm1JOTkzRVJ/3///1ji/7//f/////8kEGNUi+/G8JtrY1SL75bTbTT/f///////P4adp9MF7OKkhp2n01wUTvz/f///////P0W1EeSKExiMRbUR5O7NMEn/f///hZb/vzxWBvnW6DxsPFYG+fTY0zf/f///hZb/v+lh/tpuvnnI/3///4WW/78afJbYTvLUv/9///////+/dnsz2H7w2b//f/////8jbP9///9Y4v+/8GOG2FTA/MY=")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_ea6vo")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_qf7vx"]
data = PackedVector3Array(0.4586, 0.05, 0.5586, 1, 0.05, 1, 0.05, 0.05, 1, -1, 0.05, 1, -0.5103, 0.05, 0.4931, -0.05, 0.05, 1, -0.4206, 0.05, -0.7362, -1, 0.05, -1, -0.05, 0.05, -1, 0.5094, 0.02, -0.5603, 0.7137, 0.05, -0.05, 0.6512, 0.02, 0, 0.5094, 0.02, -0.5603, 0.5564, 0.05, -0.6138, 0.7137, 0.05, -0.05, 0.4586, 0.05, 0.5586, 0.6512, 0.02, 0, 0.7137, 0.05, 0.05, 0.4586, 0.05, 0.5586, 0.4223, 0.02, 0.5223, 0.6512, 0.02, 0, -0.1383, 0.02, -0.3348, -0.2553, 0.05, 0.0949, -0.3254, 0.02, 0.1264, -0.1383, 0.02, -0.3348, -0.0762, 0.05, -0.2603, -0.2553, 0.05, 0.0949, -0.3736, 0.02, -0.6913, -0.05, 0.05, -0.7672, 0, 0.02, -0.7446, -0.3736, 0.02, -0.6913, -0.4206, 0.05, -0.7362, -0.05, 0.05, -0.7672, -0.5103, 0.05, 0.4931, 0, 0.02, 0.7136, -0.05, 0.05, 0.7361, -0.5103, 0.05, 0.4931, -0.4741, 0.02, 0.4568, 0, 0.02, 0.7136, -0.05, 0.05, -1, 0, 0.02, -0.7446, -0.05, 0.05, -0.7672, -0.05, 0.05, -1, 0, 0.02, -1, 0, 0.02, -0.7446, 0, 0.02, -1, 0.05, 0.05, -0.7672, 0, 0.02, -0.7446, 0, 0.02, -1, 0.05, 0.05, -1, 0.05, 0.05, -0.7672, 0.7137, 0.05, 0.05, 1, 0.02, 0, 1, 0.05, 0.05, 0.7137, 0.05, 0.05, 0.6512, 0.02, 0, 1, 0.02, 0, 0.6512, 0.02, 0, 1, 0.05, -0.05, 1, 0.02, 0, 0.6512, 0.02, 0, 0.7137, 0.05, -0.05, 1, 0.05, -0.05, -0.05, 0.05, 0.7361, 0, 0.02, 1, -0.05, 0.05, 1, -0.05, 0.05, 0.7361, 0, 0.02, 0.7136, 0, 0.02, 1, 0, 0.02, 0.7136, 0.05, 0.05, 1, 0, 0.02, 1, 0, 0.02, 0.7136, 0.05, 0.05, 0.7361, 0.05, 0.05, 1, -1, 0.05, 0.05, -0.7446, 0.02, 0, -0.7672, 0.05, 0.05, -1, 0.05, 0.05, -1, 0.02, 0, -0.7446, 0.02, 0, -1, 0.02, 0, -0.7672, 0.05, -0.05, -0.7446, 0.02, 0, -1, 0.02, 0, -1, 0.05, -0.05, -0.7672, 0.05, -0.05, 0.5564, 0.05, -0.6138, 1, 0.05, -1, 1, 0.05, -0.05, -1, 0.05, 0.05, -1, -0.1, 1, -1, 0.02, 0, -1, 0.05, 0.05, -1, 0.05, 1, -1, -0.1, 1, 0.05, 0.05, 1, 1, -0.1, 1, 0, 0.02, 1, 0.05, 0.05, 1, 1, 0.05, 1, 1, -0.1, 1, -1, 0.02, 0, -1, -0.1, 1, -1, -0.1, -1, -0.05, 0.05, 1, -1, -0.1, 1, -1, 0.05, 1, -0.05, 0.05, 1, 0, 0.02, 1, -1, -0.1, 1, 1, 0.05, -0.05, 1, -0.1, -1, 1, 0.02, 0, 1, 0.05, -0.05, 1, 0.05, -1, 1, -0.1, -1, 0, 0.02, 1, 1, -0.1, 1, -1, -0.1, 1, 1, 0.02, 0, 1, 0.05, 1, 1, 0.05, 0.05, 1, 0.02, 0, 1, -0.1, 1, 1, 0.05, 1, -0.05, 0.05, -1, -1, -0.1, -1, 0, 0.02, -1, -0.05, 0.05, -1, -1, 0.05, -1, -1, -0.1, -1, 0, 0.02, -1, 1, 0.05, -1, 0.05, 0.05, -1, 0, 0.02, -1, 1, -0.1, -1, 1, 0.05, -1, -1, 0.02, 0, -1, 0.05, -1, -1, 0.05, -0.05, -1, 0.02, 0, -1, -0.1, -1, -1, 0.05, -1, 1, 0.02, 0, 1, -0.1, -1, 1, -0.1, 1, 0.5564, 0.05, -0.6138, 1, 0.05, -0.05, 0.7137, 0.05, -0.05, 0.4586, 0.05, 0.5586, 0.05, 0.05, 1, 0.05, 0.05, 0.7361, -0.5103, 0.05, 0.4931, -1, 0.05, 0.05, -0.7672, 0.05, 0.05, -0.4206, 0.05, -0.7362, -0.05, 0.05, -1, -0.05, 0.05, -0.7672, 0, 0.02, -1, -1, -0.1, -1, 1, -0.1, -1, 1, 0.05, 0.05, 1, 0.05, 1, 0.4586, 0.05, 0.5586, 0.05, 0.05, -0.7672, 0.05, 0.05, -1, 0.5564, 0.05, -0.6138, -0.7672, 0.05, 0.05, -0.4741, 0.02, 0.4568, -0.5103, 0.05, 0.4931, -0.7672, 0.05, 0.05, -0.7446, 0.02, 0, -0.4741, 0.02, 0.4568, 0, 0.02, -0.7446, -0.1383, 0.02, -0.3348, -0.3736, 0.02, -0.6913, 0, 0.02, -0.7446, 0.5564, 0.05, -0.6138, 0.5094, 0.02, -0.5603, 0, 0.02, -0.7446, 0.05, 0.05, -0.7672, 0.5564, 0.05, -0.6138, 0, 0.02, 0.7136, -0.4741, 0.02, 0.4568, 0.0321, 0.02, 0.3753, -0.7672, 0.05, -0.05, -1, 0.05, -0.05, -0.4206, 0.05, -0.7362, 0.7137, 0.05, 0.05, 1, 0.05, 0.05, 0.4586, 0.05, 0.5586, -0.7446, 0.02, 0, -0.4206, 0.05, -0.7362, -0.3736, 0.02, -0.6913, -0.7446, 0.02, 0, -0.7672, 0.05, -0.05, -0.4206, 0.05, -0.7362, 0.6512, 0.02, 0, 0.2715, 0.02, -0.2012, 0.5094, 0.02, -0.5603, 0.05, 0.05, 0.7361, 0.4223, 0.02, 0.5223, 0.4586, 0.05, 0.5586, 0.05, 0.05, 0.7361, 0, 0.02, 0.7136, 0.4223, 0.02, 0.5223, -1, 0.05, -0.05, -1, 0.05, -1, -0.4206, 0.05, -0.7362, -0.05, 0.05, 0.7361, -0.05, 0.05, 1, -0.5103, 0.05, 0.4931, 0.05, 0.05, -1, 1, 0.05, -1, 0.5564, 0.05, -0.6138, -1, 0.05, 0.05, -0.5103, 0.05, 0.4931, -1, 0.05, 1, -0.1383, 0.02, -0.3348, -0.7446, 0.02, 0, -0.3736, 0.02, -0.6913, 0.2715, 0.02, -0.2012, -0.0762, 0.05, -0.2603, -0.1383, 0.02, -0.3348, 0.2715, 0.02, -0.2012, 0.2331, 0.05, -0.1674, -0.0762, 0.05, -0.2603, -0.0762, 0.05, -0.2603, 0.0435, 0.05, 0.3003, -0.2553, 0.05, 0.0949, 0.0435, 0.05, 0.3003, 0.2331, 0.05, -0.1674, 0.2861, 0.05, 0.2103, 0.0435, 0.05, 0.3003, -0.0762, 0.05, -0.2603, 0.2331, 0.05, -0.1674, 0.3575, 0.02, 0.2514, 0.2331, 0.05, -0.1674, 0.2715, 0.02, -0.2012, 0.3575, 0.02, 0.2514, 0.2861, 0.05, 0.2103, 0.2331, 0.05, -0.1674, 0.0321, 0.02, 0.3753, -0.4741, 0.02, 0.4568, -0.3254, 0.02, 0.1264, 0.0321, 0.02, 0.3753, -0.2553, 0.05, 0.0949, 0.0435, 0.05, 0.3003, 0.0321, 0.02, 0.3753, -0.3254, 0.02, 0.1264, -0.2553, 0.05, 0.0949, 0.0321, 0.02, 0.3753, 0.2861, 0.05, 0.2103, 0.3575, 0.02, 0.2514, 0.0321, 0.02, 0.3753, 0.0435, 0.05, 0.3003, 0.2861, 0.05, 0.2103, -0.4741, 0.02, 0.4568, -0.7446, 0.02, 0, -0.3254, 0.02, 0.1264, 0.4223, 0.02, 0.5223, 0.0321, 0.02, 0.3753, 0.3575, 0.02, 0.2514, 0.4223, 0.02, 0.5223, 0, 0.02, 0.7136, 0.0321, 0.02, 0.3753, 0.6512, 0.02, 0, 0.3575, 0.02, 0.2514, 0.2715, 0.02, -0.2012, 0.4223, 0.02, 0.5223, 0.3575, 0.02, 0.2514, 0.6512, 0.02, 0, 0, 0.02, -0.7446, 0.2715, 0.02, -0.2012, -0.1383, 0.02, -0.3348, 0.5094, 0.02, -0.5603, 0.2715, 0.02, -0.2012, 0, 0.02, -0.7446, -0.3254, 0.02, 0.1264, -0.7446, 0.02, 0, -0.1383, 0.02, -0.3348)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_r47ox"]
resource_name = "texture"
albedo_texture = ExtResource("1_mku4j")
roughness = 0.45

[sub_resource type="ArrayMesh" id="ArrayMesh_wp176"]
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.15, 2),
"format": 34359742465,
"index_count": 228,
"index_data": PackedByteArray("JQADABYAAgAiABcAJgABABAAIwAHAAkAIwAhAAcAJQAJAAgAJQAkAAkAKAAPACcAJwAFAAYAJwAmAAUAIgAMAAsAIgAgAAwAEAAGAAUAEAASAAYAEgAEAAYAEgARAAQACAAVABQACAAJABUACQATABUACQAHABMACwAYABcACwAMABgADAAWABgADAAKABYAGQAPAA4AGQAbAA8AGwANAA8AGwAaAA0AIQAAABMAGQAeABsAGQACAB4AFgAfABgAFgADAB8AGwAeAB0AFwAeAAIAFwAYAB4AEwAcABUAEwAAABwAGAAfAB4AFQADABQAFQAfAAMAEAAdABIAEAABAB0AEgAAABEAEgAcAAAAGwABABoAGwAdAAEAFQAcAB8AIQATAAcAJQAWAAoAIgAZAA4AJgAQAAUAEgAdABwAFAADACUABAARACEADgAgACIADgAPACAAKAAMACAABgAhACMABgAEACEACQAoACMADQAaACYACAAUACUADwAmACcADwANACYABgAoACcACgAkACUACgAMACQAGgABACYACwAXACIAEQAAACEAGQAiAAIAIAAPACgAJAAoAAkAIwAoAAYAJAAMACgA"),
"name": "texture",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 41,
"vertex_data": PackedByteArray("AACAP87MTD0AAIC/AACAv87MTD0AAIC/AACAv87MTD0AAIA/AACAP87MTD0AAIA/zMxMPc7MTD3Ryiq/zMxMvc7MTD3Ryiq/AACAMA3XozzkBiW/a2REP87MTD3MzEy9a2REP87MTD3MzEw9faA+Pw3XozwAAIAwzMxMPc7MTD3Ryio/zMxMvc7MTD3Ryio/AACAsA3XozzjBiU/a2REv87MTD3MzEy9a2REv87MTD3MzEw9fqA+vw3XozwAAICwzMxMvc7MTD0AAIC/zMxMPc7MTD0AAIC/AAAAAA3XozwAAIC/AACAP87MTD3MzEy9AACAP87MTD3MzEw9AACAPw3XozwAAACAzMxMPc7MTD0AAIA/zMxMvc7MTD0AAIA/AAAAAA3XozwAAIA/AACAv87MTD3MzEw9AACAv87MTD3MzEy9AACAvw3XozwAAACAAACAP87MzL0AAIC/AACAv87MzL0AAIC/AACAv87MzL0AAIA/AACAP87MzL0AAIA/FzrYvg3XozwWOtg+Av8OP87MTD3Ryuq+0crqvs7MTD3Ryuo+pbYFPw3XozwXOti+pbYFPw3XozwWOtg+Av8OP87MTD3Ryuo+a2SEvs7MTD2el7e+Yqdjvg3XozzkBqW+zMzMPQ3XozzQzEy9")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_4jqne"]
resource_name = "floor_tile_small_broken_B_floor_tile_small_broken_B"
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.15, 2),
"attribute_data": PackedByteArray("Me44PyiSBD4x7jg/uEElPjHuOD+4QSU+zxEiPyiSBD7PESI/uEElPs8RIj+4QSU+zxEiPyiSBD7PESI/uEElPs8RIj+4QSU+Me44PyiSBD4x7jg/uEElPjHuOD+4QSU+RhIuP1AkyT1GEi4/UCTpPUYSLj8okgQ+uu0sP1AkyT267Sw/KJIEPrrtLD8okhQ+AIAtP0zxBT4AgC0/TPEFPgCALT9M8RU+AIAtP0zxJT4AgC0/TPE1PisgNT9QJMk9KyA1PyiSBD4rIDU/KJIEPisgNT8okiQ+Sd40P0zxBT5J3jQ/TPElPkneND9M8UU+RhIuP1Ak6T1GEi4/KJIEPkYSLj8okiQ+uu0sPyiSBD667Sw/KJIUPrrtLD8okiQ+AIAtP0zxFT4AgC0/TPElPgCALT9M8TU+AIAtP0zxRT4AgC0/TPFFPtXfJT9QJMk91d8lPyiSBD7V3yU/KJIEPtXfJT8okiQ+uCEmP0zxBT64ISY/TPElPrghJj9M8UU+uu0sP7hBBT667Sw/uEEVPrrtLD+4QSU+RhIuP7hBBT5GEi4/uEEVPkYSLj+4QSU+AIAtP7hBFT4AgC0/uEEVPgCALT+4QSU+Me44P7hBBT4x7jg/uEEVPjHuOD+4QSU+Me44P7hBBT4x7jg/uEEVPjHuOD+4QSU+Me44P7hBFT4x7jg/uEEVPjHuOD+4QSU+RhIuP7hBBT5GEi4/uEEVPkYSLj+4QSU+uu0sP7hBBT667Sw/uEEVPrrtLD+4QSU+AIAtP7hBFT4AgC0/uEEVPgCALT+4QSU+zxEiP7hBBT7PESI/uEEVPs8RIj+4QSU+zxEiP7hBBT7PESI/uEEVPs8RIj+4QSU+zxEiP7hBFT7PESI/uEEVPs8RIj+4QSU+Me44P7hBJT4x7jg/uEElPs8RIj+4QSU+zxEiP7hBJT7PESI/uEElPs8RIj+4QSU+Me44P7hBJT4x7jg/uEElPtzQKT9M8SU+3NApP0zxRT44mTE/UCTJPTiZMT8okgQ+PIAzPyiSBD7IZik/KJIEPshmKT8okiQ+JC8xP0zxBT4kLzE/TPElPiQvMT9M8SU+JC8xP0zxRT44mTE/KJIEPjiZMT8okiQ+PIAzPyiSBD7Efyc/KJIEPshmKT9QJMk9yGYpPyiSBD7c0Ck/TPEFPtzQKT9M8SU+AIAtP0zxJT4="),
"format": 34359742487,
"index_count": 228,
"index_data": PackedByteArray("aQAJAEIABgBhAEUAagADADAAYwAXABsAYwBeABcAaAAdABoAaABmAB0AbwAuAG4AbQAPABMAbQBrAA8AYgAoACMAYgBdACgAMQAWABEAMQA3ABYANgANABQANgA0AA0AGgBAAD0AGgAdAEAAGwA6AD8AGwAXADoAIgBJAEYAIgAmAEkAJABDAEgAJAAeAEMATAAvACwATABSAC8AUQApAC0AUQBPACkAYAAAADkATQBYAFMATQAHAFgARABaAEoARAAKAFoAUwBYAFYARwBZAAgARwBKAFkAOwBVAEEAOwACAFUASgBaAFkAQQALAD4AQQBbAAsAMgBXADgAMgAFAFcAOAABADUAOABUAAEAUwAEAFAAUwBWAAQAQQBVAFsAXwA5ABgAZwBCAB8AYQBLACsAbAAwABAAOABXAFQAPAAJAGkADgAzAGAALABdAGIALAAvAF0AbwAlAFwAEgBeAGMAEgAMAF4AHABvAGQAKgBOAGoAGQA8AGkALQBrAG0ALQApAGsAFQBvAG4AIABmAGgAIAAnAGYATgADAGoAIQBFAGEAMwAAAGAASwBhAAYAXAAuAG8AZQBvABwAZABvABUAZQAlAG8A"),
"material": SubResource("StandardMaterial3D_r47ox"),
"name": "texture",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 112,
"vertex_data": PackedByteArray("AACAP87MTD0AAIC/AACAP87MTD0AAIC/AACAP87MTD0AAIC/AACAv87MTD0AAIC/AACAv87MTD0AAIC/AACAv87MTD0AAIC/AACAv87MTD0AAIA/AACAv87MTD0AAIA/AACAv87MTD0AAIA/AACAP87MTD0AAIA/AACAP87MTD0AAIA/AACAP87MTD0AAIA/zMxMPc7MTD3Ryiq/zMxMPc7MTD3Ryiq/zMxMPc7MTD3Ryiq/zMxMvc7MTD3Ryiq/zMxMvc7MTD3Ryiq/zMxMvc7MTD3Ryiq/AACAMA3XozzkBiW/AACAMA3XozzkBiW/AACAMA3XozzkBiW/AACAMA3XozzkBiW/AACAMA3XozzkBiW/a2REP87MTD3MzEy9a2REP87MTD3MzEy9a2REP87MTD3MzEw9a2REP87MTD3MzEw9faA+Pw3XozwAAIAwfaA+Pw3XozwAAIAwfaA+Pw3XozwAAIAwzMxMPc7MTD3Ryio/zMxMPc7MTD3Ryio/zMxMPc7MTD3Ryio/zMxMvc7MTD3Ryio/zMxMvc7MTD3Ryio/zMxMvc7MTD3Ryio/AACAsA3XozzjBiU/AACAsA3XozzjBiU/AACAsA3XozzjBiU/AACAsA3XozzjBiU/AACAsA3XozzjBiU/a2REv87MTD3MzEy9a2REv87MTD3MzEy9a2REv87MTD3MzEw9a2REv87MTD3MzEw9fqA+vw3XozwAAICwfqA+vw3XozwAAICwfqA+vw3XozwAAICwzMxMvc7MTD0AAIC/zMxMvc7MTD0AAIC/zMxMvc7MTD0AAIC/zMxMPc7MTD0AAIC/zMxMPc7MTD0AAIC/zMxMPc7MTD0AAIC/AAAAAA3XozwAAIC/AAAAAA3XozwAAIC/AAAAAA3XozwAAIC/AACAP87MTD3MzEy9AACAP87MTD3MzEy9AACAP87MTD3MzEy9AACAP87MTD3MzEw9AACAP87MTD3MzEw9AACAP87MTD3MzEw9AACAPw3XozwAAACAAACAPw3XozwAAACAAACAPw3XozwAAACAzMxMPc7MTD0AAIA/zMxMPc7MTD0AAIA/zMxMPc7MTD0AAIA/zMxMvc7MTD0AAIA/zMxMvc7MTD0AAIA/zMxMvc7MTD0AAIA/AAAAAA3XozwAAIA/AAAAAA3XozwAAIA/AAAAAA3XozwAAIA/AACAv87MTD3MzEw9AACAv87MTD3MzEw9AACAv87MTD3MzEw9AACAv87MTD3MzEy9AACAv87MTD3MzEy9AACAv87MTD3MzEy9AACAvw3XozwAAACAAACAvw3XozwAAACAAACAvw3XozwAAACAAACAP87MzL0AAIC/AACAP87MzL0AAIC/AACAv87MzL0AAIC/AACAv87MzL0AAIC/AACAv87MzL0AAIA/AACAv87MzL0AAIA/AACAP87MzL0AAIA/AACAP87MzL0AAIA/FzrYvg3XozwWOtg+FzrYvg3XozwWOtg+Av8OP87MTD3Ryuq+Av8OP87MTD3Ryuq+Av8OP87MTD3Ryuq+0crqvs7MTD3Ryuo+0crqvs7MTD3Ryuo+pbYFPw3XozwXOti+pbYFPw3XozwXOti+pbYFPw3XozwWOtg+pbYFPw3XozwWOtg+Av8OP87MTD3Ryuo+Av8OP87MTD3Ryuo+Av8OP87MTD3Ryuo+a2SEvs7MTD2el7e+a2SEvs7MTD2el7e+a2SEvs7MTD2el7e+Yqdjvg3XozzkBqW+Yqdjvg3XozzkBqW+zMzMPQ3XozzQzEy9/3//////QWP/////////v////3////+//3///6DE/78AAP9/////v/////////+//3///////x8AAP9/////v/9//3////8//3///3y5/z//f/9/////P////3////+/EWHnyNXH3cgRYefIHNo2K/9////E2v8/2qiOyxn0ZJX/f/////8vZtqojstleYVGKV4cytPEkMrcp/DKkfX6lSleHMopike1/3///////7/cp/DK/NLcqeZpUcmZ0ZTD/3///0Gr/z//f/////9d6lJJ5emV+AthuWwsygjWoML/f///////vyxKuOw4+MVe6EgR4bKDDtD/f/////+c0uhIEeGu7roj/3///7zU/7+stmvfmJg5OKy2a9+Zw3dIHUop3uOE+83/f///////v9K1ct0NmLo5HUop3jzrOiLStXLdg8GlSZ6KxctX/oaz/3//////1Cf/f///Erf/v9y10+2+z8E+aYwOy879tLH/f///////v6G1i+6fzeI9/3//////L2b/r//P/8//p/////////+//3//////xjH/T//P/8//J/////////+//0//z//P/yf/r//P/8//p/////////+//3///0Gr/z//f//PWPGH0////3////+//3//////oJX/r///////v////3////+//3//z////z//r///e+8EVv///3////+//3//////nNL/T//Pd45WxP9//3////8//3//////grH/r//P/8//p/9//3////8//0//z//P/yf/r//POY+RRP9//3////8//3///////x//r///3u2ZPAAA/3////+//3///6DE/7//f//P////PwAA/3////+//3//z3XwFb3/r///////vwAA/3////+//////////7////9/////vwAA/3////+//////////78AAP9/////v/9//3////8//3//f////z////9/////v/9///////+/X7tO5b3FyT8DY8fDjsG+v/9///9Bq/8//3//////Alv/f///////H1G7sORoxM0/hGPEw5zCvL//f///////v/9///////+/xEOD4/3/RF7/f/////9irchDA+P9/85e/3//////oJX/f///oMT/v46bTsfV/0Hf/3//////L2a6m03H2P9u3/9///////+//3///////78=")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_wp176")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_q21ax"]
data = PackedVector3Array(0.5586, 0.05, 0.4586, 1, 0.05, 1, 0.05, 0.05, 1, -1, 0.05, 1, -0.4586, 0.05, 0.4586, -0.05, 0.05, 1, -0.2586, 0.05, -0.3586, -1, 0.05, -1, -0.05, 0.05, -1, 0.5223, 0.02, -0.4223, 0.7672, 0.05, -0.05, 0.7446, 0.02, 0, 0.5223, 0.02, -0.4223, 0.5586, 0.05, -0.4586, 0.7672, 0.05, -0.05, 0.5586, 0.05, 0.4586, 0.7446, 0.02, 0, 0.7672, 0.05, 0.05, 0.5586, 0.05, 0.4586, 0.5223, 0.02, 0.4223, 0.7446, 0.02, 0, 0.1, 0.02, -0.05, -0.7446, 0.02, 0, -0.2223, 0.02, -0.3223, -0.2223, 0.02, -0.3223, -0.05, 0.05, -0.6672, 0, 0.02, -0.6446, -0.2223, 0.02, -0.3223, -0.2586, 0.05, -0.3586, -0.05, 0.05, -0.6672, -0.4586, 0.05, 0.4586, 0, 0.02, 0.6446, -0.05, 0.05, 0.6672, -0.4586, 0.05, 0.4586, -0.4223, 0.02, 0.4223, 0, 0.02, 0.6446, -0.05, 0.05, -1, 0, 0.02, -0.6446, -0.05, 0.05, -0.6672, -0.05, 0.05, -1, 0, 0.02, -1, 0, 0.02, -0.6446, 0, 0.02, -1, 0.05, 0.05, -0.6672, 0, 0.02, -0.6446, 0, 0.02, -1, 0.05, 0.05, -1, 0.05, 0.05, -0.6672, 0.7672, 0.05, 0.05, 1, 0.02, 0, 1, 0.05, 0.05, 0.7672, 0.05, 0.05, 0.7446, 0.02, 0, 1, 0.02, 0, 0.7446, 0.02, 0, 1, 0.05, -0.05, 1, 0.02, 0, 0.7446, 0.02, 0, 0.7672, 0.05, -0.05, 1, 0.05, -0.05, -0.05, 0.05, 0.6672, 0, 0.02, 1, -0.05, 0.05, 1, -0.05, 0.05, 0.6672, 0, 0.02, 0.6446, 0, 0.02, 1, 0, 0.02, 0.6446, 0.05, 0.05, 1, 0, 0.02, 1, 0, 0.02, 0.6446, 0.05, 0.05, 0.6672, 0.05, 0.05, 1, -1, 0.05, 0.05, -0.7446, 0.02, 0, -0.7672, 0.05, 0.05, -1, 0.05, 0.05, -1, 0.02, 0, -0.7446, 0.02, 0, -1, 0.02, 0, -0.7672, 0.05, -0.05, -0.7446, 0.02, 0, -1, 0.02, 0, -1, 0.05, -0.05, -0.7672, 0.05, -0.05, 0.5586, 0.05, -0.4586, 1, 0.05, -1, 1, 0.05, -0.05, -1, 0.05, 0.05, -1, -0.1, 1, -1, 0.02, 0, -1, 0.05, 0.05, -1, 0.05, 1, -1, -0.1, 1, 0.05, 0.05, 1, 1, -0.1, 1, 0, 0.02, 1, 0.05, 0.05, 1, 1, 0.05, 1, 1, -0.1, 1, -1, 0.02, 0, -1, -0.1, 1, -1, -0.1, -1, -0.05, 0.05, 1, -1, -0.1, 1, -1, 0.05, 1, -0.05, 0.05, 1, 0, 0.02, 1, -1, -0.1, 1, 1, 0.05, -0.05, 1, -0.1, -1, 1, 0.02, 0, 1, 0.05, -0.05, 1, 0.05, -1, 1, -0.1, -1, 0, 0.02, 1, 1, -0.1, 1, -1, -0.1, 1, 1, 0.02, 0, 1, 0.05, 1, 1, 0.05, 0.05, 1, 0.02, 0, 1, -0.1, 1, 1, 0.05, 1, -0.05, 0.05, -1, -1, -0.1, -1, 0, 0.02, -1, -0.05, 0.05, -1, -1, 0.05, -1, -1, -0.1, -1, 0, 0.02, -1, 1, 0.05, -1, 0.05, 0.05, -1, 0, 0.02, -1, 1, -0.1, -1, 1, 0.05, -1, -1, 0.02, 0, -1, 0.05, -1, -1, 0.05, -0.05, -1, 0.02, 0, -1, -0.1, -1, -1, 0.05, -1, 1, 0.02, 0, 1, -0.1, -1, 1, -0.1, 1, 0.5586, 0.05, -0.4586, 1, 0.05, -0.05, 0.7672, 0.05, -0.05, 0.5586, 0.05, 0.4586, 0.05, 0.05, 1, 0.05, 0.05, 0.6672, -0.4586, 0.05, 0.4586, -1, 0.05, 0.05, -0.7672, 0.05, 0.05, -0.2586, 0.05, -0.3586, -0.05, 0.05, -1, -0.05, 0.05, -0.6672, 0, 0.02, -1, -1, -0.1, -1, 1, -0.1, -1, 1, 0.05, 0.05, 1, 0.05, 1, 0.5586, 0.05, 0.4586, 0.05, 0.05, -0.6672, 0.05, 0.05, -1, 0.5586, 0.05, -0.4586, -0.7672, 0.05, 0.05, -0.4223, 0.02, 0.4223, -0.4586, 0.05, 0.4586, -0.7672, 0.05, 0.05, -0.7446, 0.02, 0, -0.4223, 0.02, 0.4223, 0.1, 0.02, -0.05, 0, 0.02, 0.6446, -0.4223, 0.02, 0.4223, 0, 0.02, -0.6446, 0.5586, 0.05, -0.4586, 0.5223, 0.02, -0.4223, 0, 0.02, -0.6446, 0.05, 0.05, -0.6672, 0.5586, 0.05, -0.4586, 0.7446, 0.02, 0, 0.1, 0.02, -0.05, 0.5223, 0.02, -0.4223, -0.7672, 0.05, -0.05, -1, 0.05, -0.05, -0.2586, 0.05, -0.3586, 0.7672, 0.05, 0.05, 1, 0.05, 0.05, 0.5586, 0.05, 0.4586, -0.7446, 0.02, 0, -0.2586, 0.05, -0.3586, -0.2223, 0.02, -0.3223, -0.7446, 0.02, 0, -0.7672, 0.05, -0.05, -0.2586, 0.05, -0.3586, 0, 0.02, -0.6446, 0.1, 0.02, -0.05, -0.2223, 0.02, -0.3223, 0.05, 0.05, 0.6672, 0.5223, 0.02, 0.4223, 0.5586, 0.05, 0.4586, 0.05, 0.05, 0.6672, 0, 0.02, 0.6446, 0.5223, 0.02, 0.4223, -1, 0.05, -0.05, -1, 0.05, -1, -0.2586, 0.05, -0.3586, -0.05, 0.05, 0.6672, -0.05, 0.05, 1, -0.4586, 0.05, 0.4586, 0.05, 0.05, -1, 1, 0.05, -1, 0.5586, 0.05, -0.4586, -1, 0.05, 0.05, -0.4586, 0.05, 0.4586, -1, 0.05, 1, -0.4223, 0.02, 0.4223, -0.7446, 0.02, 0, 0.1, 0.02, -0.05, 0.5223, 0.02, 0.4223, 0.1, 0.02, -0.05, 0.7446, 0.02, 0, 0.5223, 0.02, -0.4223, 0.1, 0.02, -0.05, 0, 0.02, -0.6446, 0.5223, 0.02, 0.4223, 0, 0.02, 0.6446, 0.1, 0.02, -0.05)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_73u78"]
resource_name = "texture"
albedo_texture = ExtResource("1_mku4j")
roughness = 0.45

[sub_resource type="ArrayMesh" id="ArrayMesh_4wrjt"]
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.15, 2),
"format": 34359742465,
"index_count": 234,
"index_data": PackedByteArray("BwAAAAEABwAFAAAAHAADAAsAAQAXAAcAKAAbAB4AKAAnABsABQAPAAAABgAQAAQABgAWABAACQAiAB0ACQAIACIAHwAlACQAHwAgACUAEwASAAoAEwAVABIAGAAMAA0AGAAZAAwAEAAFAAQAEAAPAAUABAAHAAYABAAFAAcADAAOAA0ADAALAA4ABgAXABYABgAHABcAGAAaABkAGAAhABoAIwAWABcAGAAmACEAKQAZABoACQAmAAgACQAjACYAKQASABEAKQAKABIAFQARABIAFQAUABEACgAaABMACgApABoAEwAUABUAEwAaABQADgAIACYADgAiAAgAJwAcABsAJwARABwAGwALAB4AGwAcAAsAHwAhACAAHwAXACEAJAAXAB8AJAAjABcAJgAgACEAJgAlACAAHQAjAAkAHQAPACMAIwAlACYAIwAkACUAIgAPAB0AIgAOAA8AHgApACgAHgALACkAKAARACcAKAApABEAKQAMABkACwAMACkAIwAQABYADwAQACMADQAmABgADgAmAA0AFAACABEAHAARAAMAAwARAAIACwADAA4AAAAPAA4AAwAAAA4AAgAaACEAFAAaAAIAAQAhABcAAgAhAAEA"),
"name": "texture",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 42,
"vertex_data": PackedByteArray("AACAv83MzL0AAIC/AACAP83MzL0AAIC/AACAP83MzL0AAIA/AACAv83MzL0AAIA/mpl5v8zMTD2amXm/AACAv8vMzDwAAIC/mpl5P8zMTD2amXm/AACAP8vMzDwAAIC/zczMvMzMTD3NzMy8zczMvMzMTD0zM/O+zczMPMzMTD1mZgY/AACAv8vMzDwAAAA/mpl5v8zMTD0zM/M+mpl5v8zMTD3NzMw8AACAv8vMzDwAAACAAACAv8vMzDwAAAC/mpl5v8zMTD1mZga/AAAAAMvMzDwAAIA/zczMPMzMTD2amXk/AACAP8zMTD1mZgY/AACAP8vMzDwAAIA/AACAP8zMTD2amXk/mpl5P8zMTD1mZga/AACAP8vMzDwAAAC/mpl5P8zMTD3NzMw8mpl5P8zMTD0zM/M+AACAP8vMzDwAAAA/AACAv8zMTD2amXk/AACAv8vMzDwAAIA/AACAv8zMTD0zM/O+AACAv8zMTD1mZgY/AACAP8zMTD0zM/O+AACAP8zMTD3NzMy8AACAP8vMzDwAAACAAACAv8zMTD3NzMy8AAAAAMvMzDwAAAC/zczMPMzMTD0zM/O+zczMPMzMTD3NzMy8AAAAAMvMzDwAAACAzczMvMzMTD2amXk/zczMvMzMTD1mZgY/AAAAAMvMzDwAAAA/")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_irwer"]
resource_name = "floor_wood_small_floor_wood_small"
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.15, 2),
"attribute_data": PackedByteArray("eu4EP1CcbD567gQ/UJxsPpZSFz9QnGw+llIXP1CcbD6WUhc/UJxsPpZSFz9QnGw+eu4EP1CcbD567gQ/UJxsPnNCBj9ggeA8c0IGP2CB4DxzQgY/YIHgPHruBD+YL2U+eu4EP5gvZT4XEwY/AD7CPBcTBj8gPsI8uLAUP/C5lT24sBQ/8LmVPbiwFD/wuZU9FeAUP4CPkD0V4BQ/iI+QPZZSFz+YL2U+llIXP5gvZT45Sg0/sGgkPjlKDT+waCQ+OUoNP7BoJD44Sg0/2DzWPThKDT/YPNY9OEoNP9g81j3yqA0/YObLPfKoDT9g5ss98qgNP2Dmyz167gQ/mC9lPhcTBj80kRE+FxMGPzSRET4XEwY/WD8XPhcTBj9YPxc+dEIGPySqFD50QgY/JKoUPnRCBj8kqhQ+c0IGP8C/tj1zQgY/wL+2PXNCBj/Av7Y9eu4EP5gvZT4XEwY/8C6vPRcTBj/wLq89FxMGP3iXJz4XEwY/eJcnPnruBD+YL2U+FxMGP0Dfrz0XEwY/QN+vPRcTBj9A3889FxMGP0Dfzz1zQgY/6LSqPXNCBj/otKo9c0IGP+i0qj2WeQ0/JN4iPpZ5DT8o3iI+lnkNP5wxNj6WeQ0/nDE2PoggDj+YL2U+86gNP4RaHz7zqA0/hFofPvOoDT+EWh8+t7AUP9CMEz63sBQ/0IwTPrewFD/QjBM+FeAUP5wxNj4V4BQ/nDE2PpZSFz+YL2U+llIXP5gvZT63sBQ/4G00PrewFD/gbTQ+t7AUP+BtND64sBQ/QCcEPriwFD9AJwQ+uLAUP0AnBD4V4BQ/QN/PPRXgFD9A3889FeAUP6DvBz4V4BQ/oO8HPpZSFz+YL2U+ubAUP6gsCj65sBQ/qCwKPrmwFD+oLAo+uLAUP/R2Qz64sBQ/9HZDPriwFD/0dkM+FeAUPzSRET4V4BQ/NJERPhXgFD9YP0c+FeAUP1g/Rz6WUhc/mC9lPnNCBj+Q6TQ+c0IGP5DpND5zQgY/kOk0PnruBD+YL2U+eu4EP5gvZT4XEwY/nDE2PhcTBj+cMTY+c0IGP9g81j1zQgY/2DzWPXNCBj/YPNY9c0IGPwTAFD5zQgY/BMAUPnNCBj8EwBQ+ubAUP2DW0z25sBQ/YNbTPbmwFD9g1tM9ubAUP3g1Iz65sBQ/eDUjPrmwFD94NSM+FeAUP3iXBz4V4BQ/eJcHPhXgFD94lyc+FeAUP3iXJz6WUhc/mC9lPnNCBj+0aCQ+c0IGP7RoJD5zQgY/tGgkPpZ5DT+Avl89lnkNP4C+Xz2WeQ0/QN/PPZZ5DT9A3889lnkNP0Df3z3yqA0/gEZxPfKoDT+ARnE98qgNP4BGcT3zqA0/uDfrPfOoDT+4N+s986gNP7g36z2WeQ0/8C7fPZZ5DT/wLu89lnkNP/Au7z2WeQ0/eJcnPpZ5DT94lyc+OUoNP5DpND45Sg0/kOk0PjlKDT+Q6TQ+OEoNPwDAFD44Sg0/AMAUPjhKDT8AwBQ+lnkNP2giwz2WeQ0/aCLDPZZ5DT80kRE+lnkNPzSRET6WeQ0/WD8vPg=="),
"format": 34359742487,
"index_count": 234,
"index_data": PackedByteArray("FAABAAIAFAAMAAEAXwAGAB8AAwBQABUAiwBeAGgAiwCIAF4ACwAvAAAAEAA2AAoAEABKADYAGgB2AGUAGgAXAHYAagCBAH4AagBtAIEAQAA+AB4AQABHAD4AUgAmACkAUgBVACYANAAOAAgANAAwAA4ACQATAA8ACQANABMAJAArACcAJAAiACsAEQBPAEsAEQASAE8AUwBaAFYAUwBwAFoAewBJAE4AUQCCAG8AkQBUAFkAGwCGABgAGwB6AIYAjQA8ADcAjQAcADwARgA4AD0ARgBCADgAHQBXAD8AHQCOAFcAQQBDAEgAQQBYAEMALgAWAIUALgB1ABYAhwBiAF0AhwA5AGIAXAAgAGYAXABhACAAawByAG4AawBNAHIAfQBMAGkAfQB4AEwAhABsAHEAhACAAGwAZAB5ABkAZAAzAHkAdwB/AIMAdwB8AH8AdAAyAGMAdAAtADIAZwCPAIoAZwAhAI8AjAA6AIkAjACQADoAkQAlAFQAIwAlAJEAewA1AEkAMQA1AHsAKACCAFEALACCACgARAAEADsAYAA7AAcABwA7AAQAHwAGACoAAAAvACoABgAAACoABQBbAHMARQBbAAUAAwBzAFAABQBzAAMA"),
"material": SubResource("StandardMaterial3D_73u78"),
"name": "texture",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 146,
"vertex_data": PackedByteArray("AACAv83MzL0AAIC/AACAv83MzL0AAIC/AACAP83MzL0AAIC/AACAP83MzL0AAIC/AACAP83MzL0AAIA/AACAP83MzL0AAIA/AACAv83MzL0AAIA/AACAv83MzL0AAIA/mpl5v8zMTD2amXm/mpl5v8zMTD2amXm/mpl5v8zMTD2amXm/AACAv8vMzDwAAIC/AACAv8vMzDwAAIC/AACAv8vMzDwAAIC/AACAv8vMzDwAAIC/mpl5P8zMTD2amXm/mpl5P8zMTD2amXm/mpl5P8zMTD2amXm/AACAP8vMzDwAAIC/AACAP8vMzDwAAIC/AACAP8vMzDwAAIC/AACAP8vMzDwAAIC/zczMvMzMTD3NzMy8zczMvMzMTD3NzMy8zczMvMzMTD3NzMy8zczMvMzMTD0zM/O+zczMvMzMTD0zM/O+zczMvMzMTD0zM/O+zczMPMzMTD1mZgY/zczMPMzMTD1mZgY/zczMPMzMTD1mZgY/AACAv8vMzDwAAAA/AACAv8vMzDwAAAA/AACAv8vMzDwAAAA/AACAv8vMzDwAAAA/AACAv8vMzDwAAAA/mpl5v8zMTD0zM/M+mpl5v8zMTD0zM/M+mpl5v8zMTD0zM/M+mpl5v8zMTD3NzMw8mpl5v8zMTD3NzMw8mpl5v8zMTD3NzMw8AACAv8vMzDwAAACAAACAv8vMzDwAAACAAACAv8vMzDwAAACAAACAv8vMzDwAAACAAACAv8vMzDwAAACAAACAv8vMzDwAAAC/AACAv8vMzDwAAAC/AACAv8vMzDwAAAC/AACAv8vMzDwAAAC/AACAv8vMzDwAAAC/mpl5v8zMTD1mZga/mpl5v8zMTD1mZga/mpl5v8zMTD1mZga/AAAAAMvMzDwAAIA/AAAAAMvMzDwAAIA/AAAAAMvMzDwAAIA/AAAAAMvMzDwAAIA/AAAAAMvMzDwAAIA/zczMPMzMTD2amXk/zczMPMzMTD2amXk/zczMPMzMTD2amXk/AACAP8zMTD1mZgY/AACAP8zMTD1mZgY/AACAP8zMTD1mZgY/AACAP8vMzDwAAIA/AACAP8vMzDwAAIA/AACAP8vMzDwAAIA/AACAP8vMzDwAAIA/AACAP8zMTD2amXk/AACAP8zMTD2amXk/AACAP8zMTD2amXk/mpl5P8zMTD1mZga/mpl5P8zMTD1mZga/mpl5P8zMTD1mZga/AACAP8vMzDwAAAC/AACAP8vMzDwAAAC/AACAP8vMzDwAAAC/AACAP8vMzDwAAAC/AACAP8vMzDwAAAC/mpl5P8zMTD3NzMw8mpl5P8zMTD3NzMw8mpl5P8zMTD3NzMw8mpl5P8zMTD0zM/M+mpl5P8zMTD0zM/M+mpl5P8zMTD0zM/M+AACAP8vMzDwAAAA/AACAP8vMzDwAAAA/AACAP8vMzDwAAAA/AACAP8vMzDwAAAA/AACAP8vMzDwAAAA/AACAv8zMTD2amXk/AACAv8zMTD2amXk/AACAv8zMTD2amXk/AACAv8vMzDwAAIA/AACAv8vMzDwAAIA/AACAv8vMzDwAAIA/AACAv8vMzDwAAIA/AACAv8zMTD0zM/O+AACAv8zMTD0zM/O+AACAv8zMTD0zM/O+AACAv8zMTD1mZgY/AACAv8zMTD1mZgY/AACAv8zMTD1mZgY/AACAP8zMTD0zM/O+AACAP8zMTD0zM/O+AACAP8zMTD0zM/O+AACAP8zMTD3NzMy8AACAP8zMTD3NzMy8AACAP8zMTD3NzMy8AACAP8vMzDwAAACAAACAP8vMzDwAAACAAACAP8vMzDwAAACAAACAP8vMzDwAAACAAACAP8vMzDwAAACAAACAv8zMTD3NzMy8AACAv8zMTD3NzMy8AACAv8zMTD3NzMy8AAAAAMvMzDwAAAC/AAAAAMvMzDwAAAC/AAAAAMvMzDwAAAC/AAAAAMvMzDwAAAC/AAAAAMvMzDwAAAC/zczMPMzMTD0zM/O+zczMPMzMTD0zM/O+zczMPMzMTD0zM/O+zczMPMzMTD3NzMy8zczMPMzMTD3NzMy8zczMPMzMTD3NzMy8AAAAAMvMzDwAAACAAAAAAMvMzDwAAACAAAAAAMvMzDwAAACAAAAAAMvMzDwAAACAAAAAAMvMzDwAAACAzczMvMzMTD2amXk/zczMvMzMTD2amXk/zczMvMzMTD2amXk/zczMvMzMTD1mZgY/zczMvMzMTD1mZgY/zczMvMzMTD1mZgY/AAAAAMvMzDwAAAA/AAAAAMvMzDwAAAA/AAAAAMvMzDwAAAA/AAAAAMvMzDwAAAA/AAAAAMvMzDwAAAA/AAD/f////7//////////P/////////8/////f////7//f/9/////v////3////+/AAD/f////7//f/9/////v/8//7+CxcDi/7///3nuea7/f/////8lygAA/3////+//////////z//v///ee55rv8//7+CxcDi/7///3nuea7/f/////8lyv+//7+CxT2d/7//v4LFPZ3/v///ee55rv////////8/////f////7//f/+/////v/9////+//+//7//v/+//5//v///////v/9////+//+//7//v/+//5//P/+/FsoK5f+///9b5Fuk/3//////KtEAAP9/////vwAA/3+Mtv///7///////7//P/+/gsXA4v9//7957oXR/z//v4LFwOL/f/+/ee6F0f9//////yXK/z//v4LFwOL/v///ee55rv9//////yXKAAD/f////7//P/+/gsXA4v+///957nmuAAD/fwCA////f/+//v//vwAA/3////+//z//v4LFwOL/f/+/ee6F0QAA/3//f////7///////7//P/+/gsXA4v9//7957oXR/3//////Jcr/P/+/pb3S3v9//78i8tvN/3//v////7//v/+/SbdapP9//3////+//z//vxa+Ct//f/+/hvJ3zf9//////3rQ/7///1zdXZ3/f/////9Uz////3//f9SE/3//v1nppNb///9//39Lgv9//3////+/////f////7//f/+/h+l21v9//////5vO////f/9/GYL/f/+/ee6F0f9//////yXK/7//v4LFPZ3/v///g92Dnf///38LowCA/3//v3nuhdH/v/+/gsU9nf///3////+//7///3nuea7/f/////8lyv+//7+CxT2d/3//v3nuhdH/f/////8lyv+//7+CxT2d/7///4PdhJ3///9//3/jhP9//7957oXR/7//v4LFPZ3///9/////vwAA/385nv///3//v////7//f////v//vwAA/3////+//3//f////78AAP9/jp3///9//7////+/AAD/f/9/////v///////v/9///////+/AAD/fyS4////v///////v/9////+//+//7///1zdXJ3/f/////8w0f///38LowCA/3//vz3pwNb/f/////8w0f///38LowCA/7///3nuea7/v/+/gsU9nf9//79Z6aTW////fwujAID///9/////vwAA/38AgP///3//v/7//7//f////v//v/8//78lyhLl/7///3vkfKT/v///////v/+//7//v/+f/3//v3nuhdH/P/+/JsoS5f+///9b5Fuk/3//////MNH/P/+/JcoS5f9//7+95EHb/3//////MNH/v///ee55rv8//78lyhLl/3//v+bkGNv/f/+/////v/+//7//v/+f/3//v////7//f///////v/+//78RuPaj/7////7//7//f////v//v/+//78Kr3mo/z//v0DJn+T/v///e+R7pP+////+//+//7//v7+un6j/f/+/ee6F0Q==")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_4wrjt")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_oywtm"]
data = PackedVector3Array(1, 0.025, -1, -1, -0.1, -1, 1, -0.1, -1, 1, 0.025, -1, -1, 0.025, -1, -1, -0.1, -1, -1, 0.025, 1, -1, -0.1, 1, -1, 0.025, 0.5, 1, -0.1, -1, 1, 0.025, -0.5, 1, 0.025, -1, -0.025, 0.05, 0.525, -1, 0.05, 0.975, -1, 0.05, 0.525, -0.025, 0.05, 0.525, -0.025, 0.05, 0.975, -1, 0.05, 0.975, -1, 0.025, -1, -1, 0.025, -0.5, -1, -0.1, -1, 0.975, 0.05, -0.975, -0.975, 0.05, -0.525, -0.975, 0.05, -0.975, 0.975, 0.05, -0.975, 0.975, 0.05, -0.525, -0.975, 0.05, -0.525, -0.025, 0.05, -0.475, -1, 0.05, -0.025, -1, 0.05, -0.475, -0.025, 0.05, -0.475, -0.025, 0.05, -0.025, -1, 0.05, -0.025, 1, 0.05, -0.475, 0.025, 0.05, -0.025, 0.025, 0.05, -0.475, 1, 0.05, -0.475, 1, 0.05, -0.025, 0.025, 0.05, -0.025, 1, 0.05, 0.525, 0.025, 0.05, 0.975, 0.025, 0.05, 0.525, 1, 0.05, 0.525, 1, 0.05, 0.975, 0.025, 0.05, 0.975, 0.975, 0.05, 0.025, -0.975, 0.05, 0.475, -0.975, 0.05, 0.025, 0.975, 0.05, 0.025, 0.975, 0.05, 0.475, -0.975, 0.05, 0.475, -0.975, 0.05, -0.525, -1, 0.025, -1, -0.975, 0.05, -0.975, -0.975, 0.05, -0.525, -1, 0.025, -0.5, -1, 0.025, -1, -0.975, 0.05, -0.975, 1, 0.025, -1, 0.975, 0.05, -0.975, -0.975, 0.05, -0.975, -1, 0.025, -1, 1, 0.025, -1, -0.975, 0.05, 0.475, -1, 0.025, 0, -0.975, 0.05, 0.025, -0.975, 0.05, 0.475, -1, 0.025, 0.5, -1, 0.025, 0, 0.975, 0.05, -0.975, 1, 0.025, -0.5, 0.975, 0.05, -0.525, 0.975, 0.05, -0.975, 1, 0.025, -1, 1, 0.025, -0.5, 0.975, 0.05, 0.025, 1, 0.025, 0.5, 0.975, 0.05, 0.475, 0.975, 0.05, 0.025, 1, 0.025, 0, 1, 0.025, 0.5, 0, 0.025, -0.5, 0.975, 0.05, -0.525, 1, 0.025, -0.5, 0.975, 0.05, 0.025, 0, 0.025, 0, 1, 0.025, 0, 0, 0.025, 0.5, 0.975, 0.05, 0.475, 1, 0.025, 0.5, -0.025, 0.05, -0.475, 0, 0.025, 0, -0.025, 0.05, -0.025, -0.025, 0.05, -0.475, 0, 0.025, -0.5, 0, 0.025, 0, 0, 0.025, 0.5, 0.025, 0.05, 0.975, 0, 0.025, 1, 0, 0.025, 0.5, 0.025, 0.05, 0.525, 0.025, 0.05, 0.975, 1, 0.05, 0.975, 0, 0.025, 1, 0.025, 0.05, 0.975, 1, 0.05, 0.975, 1, 0.025, 1, 0, 0.025, 1, 0.025, 0.05, 0.525, 1, 0.025, 0.5, 1, 0.05, 0.525, 0.025, 0.05, 0.525, 0, 0.025, 0.5, 1, 0.025, 0.5, 1, 0.05, 0.525, 1, 0.025, 1, 1, 0.05, 0.975, 1, 0.05, 0.525, 1, 0.025, 0.5, 1, 0.025, 1, -1, 0.025, 0, -0.025, 0.05, -0.025, 0, 0.025, 0, -1, 0.025, 0, -1, 0.05, -0.025, -0.025, 0.05, -0.025, -0.025, 0.05, 0.975, -1, 0.025, 1, -1, 0.05, 0.975, -0.025, 0.05, 0.975, 0, 0.025, 1, -1, 0.025, 1, -1, 0.05, 0.975, -1, 0.025, 0.5, -1, 0.05, 0.525, -1, 0.05, 0.975, -1, 0.025, 1, -1, 0.025, 0.5, 1, 0.05, -0.475, 1, 0.025, 0, 1, 0.05, -0.025, 1, 0.05, -0.475, 1, 0.025, -0.5, 1, 0.025, 0, 0.025, 0.05, -0.475, 1, 0.025, -0.5, 1, 0.05, -0.475, 0.025, 0.05, -0.475, 0, 0.025, -0.5, 1, 0.025, -0.5, 0, 0.025, 0, 1, 0.05, -0.025, 1, 0.025, 0, 0, 0.025, 0, 0.025, 0.05, -0.025, 1, 0.05, -0.025, -1, 0.05, -0.475, 0, 0.025, -0.5, -0.025, 0.05, -0.475, -1, 0.05, -0.475, -1, 0.025, -0.5, 0, 0.025, -0.5, 0, 0.025, -0.5, 0.025, 0.05, -0.025, 0, 0.025, 0, 0, 0.025, -0.5, 0.025, 0.05, -0.475, 0.025, 0.05, -0.025, -1, 0.05, -0.025, -1, 0.025, -0.5, -1, 0.05, -0.475, -1, 0.05, -0.025, -1, 0.025, 0, -1, 0.025, -0.5, -1, 0.05, 0.525, 0, 0.025, 0.5, -0.025, 0.05, 0.525, -1, 0.05, 0.525, -1, 0.025, 0.5, 0, 0.025, 0.5, -0.025, 0.05, 0.525, 0, 0.025, 1, -0.025, 0.05, 0.975, -0.025, 0.05, 0.525, 0, 0.025, 0.5, 0, 0.025, 1, 0, 0.025, 0.5, -0.975, 0.05, 0.475, 0.975, 0.05, 0.475, -1, 0.025, 0.5, -0.975, 0.05, 0.475, 0, 0.025, 0.5, 0, 0.025, -0.5, -0.975, 0.05, -0.525, 0.975, 0.05, -0.525, -1, 0.025, -0.5, -0.975, 0.05, -0.525, 0, 0.025, -0.5, -0.975, 0.05, 0.025, 0, 0.025, 0, 0.975, 0.05, 0.025, -1, 0.025, 0, 0, 0.025, 0, -0.975, 0.05, 0.025, 1, 0.025, 1, 1, -0.1, 1, 0, 0.025, 1, -1, 0.025, 1, 0, 0.025, 1, -1, -0.1, 1, -1, -0.1, 1, 0, 0.025, 1, 1, -0.1, 1, -1, 0.025, 0.5, -1, -0.1, 1, -1, 0.025, 0, -1, -0.1, -1, -1, 0.025, -0.5, -1, 0.025, 0, -1, -0.1, 1, -1, -0.1, -1, -1, 0.025, 0, 1, -0.1, 1, 1, 0.025, 0.5, 1, 0.025, 0, 1, 0.025, 1, 1, 0.025, 0.5, 1, -0.1, 1, 1, -0.1, -1, 1, 0.025, 0, 1, 0.025, -0.5, 1, -0.1, 1, 1, 0.025, 0, 1, -0.1, -1)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_y13gu"]
resource_name = "texture"
albedo_texture = ExtResource("1_mku4j")
roughness = 0.45

[sub_resource type="ArrayMesh" id="ArrayMesh_4y7sm"]
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.15, 2),
"format": 34359742465,
"index_count": 234,
"index_data": PackedByteArray("BwAAAAEABwAFAAAAHAADAAsAAQAXAAcAKAAbAB4AKAAnABsABQAPAAAABgAQAAQABgAWABAACQAiAB0ACQAIACIAHwAlACQAHwAgACUAEwASAAoAEwAVABIAGAAMAA0AGAAZAAwAEAAFAAQAEAAPAAUABAAHAAYABAAFAAcADAAOAA0ADAALAA4ABgAXABYABgAHABcAGAAaABkAGAAhABoAIwAWABcAGAAmACEAKQAZABoACQAmAAgACQAjACYAKQASABEAKQAKABIAFQARABIAFQAUABEACgAaABMACgApABoAEwAUABUAEwAaABQADgAIACYADgAiAAgAJwAcABsAJwARABwAGwALAB4AGwAcAAsAHwAhACAAHwAXACEAJAAXAB8AJAAjABcAJgAgACEAJgAlACAAHQAjAAkAHQAPACMAIwAlACYAIwAkACUAIgAPAB0AIgAOAA8AHgApACgAHgALACkAKAARACcAKAApABEAKQAMABkACwAMACkAIwAQABYADwAQACMADQAmABgADgAmAA0AFAACABEAHAARAAMAAwARAAIACwADAA4AAAAPAA4AAwAAAA4AAgAaACEAFAAaAAIAAQAhABcAAgAhAAEA"),
"name": "texture",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 42,
"vertex_data": PackedByteArray("AACAv83MzL0AAIC/AACAP83MzL0AAIC/AACAP83MzL0AAIA/AACAv83MzL0AAIA/mpl5v8zMTD2amXm/AACAv8vMzDwAAIC/mpl5P8zMTD2amXm/AACAP8vMzDwAAIC/zczMvMzMTD3NzMy8zczMvMzMTD0zM/O+zczMPMzMTD1mZgY/AACAv8vMzDwAAAA/mpl5v8zMTD0zM/M+mpl5v8zMTD3NzMw8AACAv8vMzDwAAACAAACAv8vMzDwAAAC/mpl5v8zMTD1mZga/AAAAAMvMzDwAAIA/zczMPMzMTD2amXk/AACAP8zMTD1mZgY/AACAP8vMzDwAAIA/AACAP8zMTD2amXk/mpl5P8zMTD1mZga/AACAP8vMzDwAAAC/mpl5P8zMTD3NzMw8mpl5P8zMTD0zM/M+AACAP8vMzDwAAAA/AACAv8zMTD2amXk/AACAv8vMzDwAAIA/AACAv8zMTD0zM/O+AACAv8zMTD1mZgY/AACAP8zMTD0zM/O+AACAP8zMTD3NzMy8AACAP8vMzDwAAACAAACAv8zMTD3NzMy8AAAAAMvMzDwAAAC/zczMPMzMTD0zM/O+zczMPMzMTD3NzMy8AAAAAMvMzDwAAACAzczMvMzMTD2amXk/zczMvMzMTD1mZgY/AAAAAMvMzDwAAAA/")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_8trks"]
resource_name = "floor_wood_small_dark_floor_wood_small_dark"
_surfaces = [{
"aabb": AABB(-1, -0.1, -1, 2, 0.15, 2),
"attribute_data": PackedByteArray("eu5kP1CcbD567mQ/UJxsPpZSdz9QnGw+llJ3P1CcbD6WUnc/UJxsPpZSdz9QnGw+eu5kP1CcbD567mQ/UJxsPnNCZj9ggeA8c0JmP2CB4DxzQmY/YIHgPHruZD+YL2U+eu5kP5gvZT4XE2Y/AD7CPBcTZj8gPsI8uLB0P/C5lT24sHQ/8LmVPbiwdD/wuZU9FeB0P4CPkD0V4HQ/iI+QPZZSdz+YL2U+llJ3P5gvZT45Sm0/sGgkPjlKbT+waCQ+OUptP7BoJD44Sm0/2DzWPThKbT/YPNY9OEptP9g81j3yqG0/YObLPfKobT9g5ss98qhtP2Dmyz167mQ/mC9lPhcTZj80kRE+FxNmPzSRET4XE2Y/WD8XPhcTZj9YPxc+dEJmPySqFD50QmY/JKoUPnRCZj8kqhQ+c0JmP8C/tj1zQmY/wL+2PXNCZj/Av7Y9eu5kP5gvZT4XE2Y/8C6vPRcTZj/wLq89FxNmP3iXJz4XE2Y/eJcnPnruZD+YL2U+FxNmP0Dfrz0XE2Y/QN+vPRcTZj9A3889FxNmP0Dfzz1zQmY/6LSqPXNCZj/otKo9c0JmP+i0qj2WeW0/JN4iPpZ5bT8o3iI+lnltP5wxNj6WeW0/nDE2Poggbj+YL2U+86htP4RaHz7zqG0/hFofPvOobT+EWh8+t7B0P9CMEz63sHQ/0IwTPrewdD/QjBM+FeB0P5wxNj4V4HQ/nDE2PpZSdz+YL2U+llJ3P5gvZT63sHQ/4G00PrewdD/gbTQ+t7B0P+BtND64sHQ/QCcEPriwdD9AJwQ+uLB0P0AnBD4V4HQ/QN/PPRXgdD9A3889FeB0P6DvBz4V4HQ/oO8HPpZSdz+YL2U+ubB0P6gsCj65sHQ/qCwKPrmwdD+oLAo+uLB0P/R2Qz64sHQ/9HZDPriwdD/0dkM+FeB0PzSRET4V4HQ/NJERPhXgdD9YP0c+FeB0P1g/Rz6WUnc/mC9lPnNCZj+Q6TQ+c0JmP5DpND5zQmY/kOk0PnruZD+YL2U+eu5kP5gvZT4XE2Y/nDE2PhcTZj+cMTY+c0JmP9g81j1zQmY/2DzWPXNCZj/YPNY9c0JmPwTAFD5zQmY/BMAUPnNCZj8EwBQ+ubB0P2DW0z25sHQ/YNbTPbmwdD9g1tM9ubB0P3g1Iz65sHQ/eDUjPrmwdD94NSM+FeB0P3iXBz4V4HQ/eJcHPhXgdD94lyc+FeB0P3iXJz6WUnc/mC9lPnNCZj+0aCQ+c0JmP7RoJD5zQmY/tGgkPpZ5bT+Avl89lnltP4C+Xz2WeW0/QN/PPZZ5bT9A3889lnltP0Df3z3yqG0/gEZxPfKobT+ARnE98qhtP4BGcT3zqG0/uDfrPfOobT+4N+s986htP7g36z2WeW0/8C7fPZZ5bT/wLu89lnltP/Au7z2WeW0/eJcnPpZ5bT94lyc+OUptP5DpND45Sm0/kOk0PjlKbT+Q6TQ+OEptPwDAFD44Sm0/AMAUPjhKbT8AwBQ+lnltP2giwz2WeW0/aCLDPZZ5bT80kRE+lnltPzSRET6WeW0/WD8vPg=="),
"format": 34359742487,
"index_count": 234,
"index_data": PackedByteArray("FAABAAIAFAAMAAEAXwAGAB8AAwBQABUAiwBeAGgAiwCIAF4ACwAvAAAAEAA2AAoAEABKADYAGgB2AGUAGgAXAHYAagCBAH4AagBtAIEAQAA+AB4AQABHAD4AUgAmACkAUgBVACYANAAOAAgANAAwAA4ACQATAA8ACQANABMAJAArACcAJAAiACsAEQBPAEsAEQASAE8AUwBaAFYAUwBwAFoAewBJAE4AUQCCAG8AkQBUAFkAGwCGABgAGwB6AIYAjQA8ADcAjQAcADwARgA4AD0ARgBCADgAHQBXAD8AHQCOAFcAQQBDAEgAQQBYAEMALgAWAIUALgB1ABYAhwBiAF0AhwA5AGIAXAAgAGYAXABhACAAawByAG4AawBNAHIAfQBMAGkAfQB4AEwAhABsAHEAhACAAGwAZAB5ABkAZAAzAHkAdwB/AIMAdwB8AH8AdAAyAGMAdAAtADIAZwCPAIoAZwAhAI8AjAA6AIkAjACQADoAkQAlAFQAIwAlAJEAewA1AEkAMQA1AHsAKACCAFEALACCACgARAAEADsAYAA7AAcABwA7AAQAHwAGACoAAAAvACoABgAAACoABQBbAHMARQBbAAUAAwBzAFAABQBzAAMA"),
"material": SubResource("StandardMaterial3D_y13gu"),
"name": "texture",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 146,
"vertex_data": PackedByteArray("AACAv83MzL0AAIC/AACAv83MzL0AAIC/AACAP83MzL0AAIC/AACAP83MzL0AAIC/AACAP83MzL0AAIA/AACAP83MzL0AAIA/AACAv83MzL0AAIA/AACAv83MzL0AAIA/mpl5v8zMTD2amXm/mpl5v8zMTD2amXm/mpl5v8zMTD2amXm/AACAv8vMzDwAAIC/AACAv8vMzDwAAIC/AACAv8vMzDwAAIC/AACAv8vMzDwAAIC/mpl5P8zMTD2amXm/mpl5P8zMTD2amXm/mpl5P8zMTD2amXm/AACAP8vMzDwAAIC/AACAP8vMzDwAAIC/AACAP8vMzDwAAIC/AACAP8vMzDwAAIC/zczMvMzMTD3NzMy8zczMvMzMTD3NzMy8zczMvMzMTD3NzMy8zczMvMzMTD0zM/O+zczMvMzMTD0zM/O+zczMvMzMTD0zM/O+zczMPMzMTD1mZgY/zczMPMzMTD1mZgY/zczMPMzMTD1mZgY/AACAv8vMzDwAAAA/AACAv8vMzDwAAAA/AACAv8vMzDwAAAA/AACAv8vMzDwAAAA/AACAv8vMzDwAAAA/mpl5v8zMTD0zM/M+mpl5v8zMTD0zM/M+mpl5v8zMTD0zM/M+mpl5v8zMTD3NzMw8mpl5v8zMTD3NzMw8mpl5v8zMTD3NzMw8AACAv8vMzDwAAACAAACAv8vMzDwAAACAAACAv8vMzDwAAACAAACAv8vMzDwAAACAAACAv8vMzDwAAACAAACAv8vMzDwAAAC/AACAv8vMzDwAAAC/AACAv8vMzDwAAAC/AACAv8vMzDwAAAC/AACAv8vMzDwAAAC/mpl5v8zMTD1mZga/mpl5v8zMTD1mZga/mpl5v8zMTD1mZga/AAAAAMvMzDwAAIA/AAAAAMvMzDwAAIA/AAAAAMvMzDwAAIA/AAAAAMvMzDwAAIA/AAAAAMvMzDwAAIA/zczMPMzMTD2amXk/zczMPMzMTD2amXk/zczMPMzMTD2amXk/AACAP8zMTD1mZgY/AACAP8zMTD1mZgY/AACAP8zMTD1mZgY/AACAP8vMzDwAAIA/AACAP8vMzDwAAIA/AACAP8vMzDwAAIA/AACAP8vMzDwAAIA/AACAP8zMTD2amXk/AACAP8zMTD2amXk/AACAP8zMTD2amXk/mpl5P8zMTD1mZga/mpl5P8zMTD1mZga/mpl5P8zMTD1mZga/AACAP8vMzDwAAAC/AACAP8vMzDwAAAC/AACAP8vMzDwAAAC/AACAP8vMzDwAAAC/AACAP8vMzDwAAAC/mpl5P8zMTD3NzMw8mpl5P8zMTD3NzMw8mpl5P8zMTD3NzMw8mpl5P8zMTD0zM/M+mpl5P8zMTD0zM/M+mpl5P8zMTD0zM/M+AACAP8vMzDwAAAA/AACAP8vMzDwAAAA/AACAP8vMzDwAAAA/AACAP8vMzDwAAAA/AACAP8vMzDwAAAA/AACAv8zMTD2amXk/AACAv8zMTD2amXk/AACAv8zMTD2amXk/AACAv8vMzDwAAIA/AACAv8vMzDwAAIA/AACAv8vMzDwAAIA/AACAv8vMzDwAAIA/AACAv8zMTD0zM/O+AACAv8zMTD0zM/O+AACAv8zMTD0zM/O+AACAv8zMTD1mZgY/AACAv8zMTD1mZgY/AACAv8zMTD1mZgY/AACAP8zMTD0zM/O+AACAP8zMTD0zM/O+AACAP8zMTD0zM/O+AACAP8zMTD3NzMy8AACAP8zMTD3NzMy8AACAP8zMTD3NzMy8AACAP8vMzDwAAACAAACAP8vMzDwAAACAAACAP8vMzDwAAACAAACAP8vMzDwAAACAAACAP8vMzDwAAACAAACAv8zMTD3NzMy8AACAv8zMTD3NzMy8AACAv8zMTD3NzMy8AAAAAMvMzDwAAAC/AAAAAMvMzDwAAAC/AAAAAMvMzDwAAAC/AAAAAMvMzDwAAAC/AAAAAMvMzDwAAAC/zczMPMzMTD0zM/O+zczMPMzMTD0zM/O+zczMPMzMTD0zM/O+zczMPMzMTD3NzMy8zczMPMzMTD3NzMy8zczMPMzMTD3NzMy8AAAAAMvMzDwAAACAAAAAAMvMzDwAAACAAAAAAMvMzDwAAACAAAAAAMvMzDwAAACAAAAAAMvMzDwAAACAzczMvMzMTD2amXk/zczMvMzMTD2amXk/zczMvMzMTD2amXk/zczMvMzMTD1mZgY/zczMvMzMTD1mZgY/zczMvMzMTD1mZgY/AAAAAMvMzDwAAAA/AAAAAMvMzDwAAAA/AAAAAMvMzDwAAAA/AAAAAMvMzDwAAAA/AAAAAMvMzDwAAAA/AAD/f////7//////////P/////////8/////f////7//f/9/////v////3////+/AAD/f////7//f/9/////v/8//7+CxcDi/7///3nuea7/f/////8lygAA/3////+//////////z//v///ee55rv8//7+CxcDi/7///3nuea7/f/////8lyv+//7+CxT2d/7//v4LFPZ3/v///ee55rv////////8/////f////7//f/+/////v/9////+//+//7//v/+//5//v///////v/9////+//+//7//v/+//5//P/+/FsoK5f+///9b5Fuk/3//////KtEAAP9/////vwAA/3+Mtv///7///////7//P/+/gsXA4v9//7957oXR/z//v4LFwOL/f/+/ee6F0f9//////yXK/z//v4LFwOL/v///ee55rv9//////yXKAAD/f////7//P/+/gsXA4v+///957nmuAAD/fwCA////f/+//v//vwAA/3////+//z//v4LFwOL/f/+/ee6F0QAA/3//f////7///////7//P/+/gsXA4v9//7957oXR/3//////Jcr/P/+/pb3S3v9//78i8tvN/3//v////7//v/+/SbdapP9//3////+//z//vxa+Ct//f/+/hvJ3zf9//////3rQ/7///1zdXZ3/f/////9Uz////3//f9SE/3//v1nppNb///9//39Lgv9//3////+/////f////7//f/+/h+l21v9//////5vO////f/9/GYL/f/+/ee6F0f9//////yXK/7//v4LFPZ3/v///g92Dnf///38LowCA/3//v3nuhdH/v/+/gsU9nf///3////+//7///3nuea7/f/////8lyv+//7+CxT2d/3//v3nuhdH/f/////8lyv+//7+CxT2d/7///4PdhJ3///9//3/jhP9//7957oXR/7//v4LFPZ3///9/////vwAA/385nv///3//v////7//f////v//vwAA/3////+//3//f////78AAP9/jp3///9//7////+/AAD/f/9/////v///////v/9///////+/AAD/fyS4////v///////v/9////+//+//7///1zdXJ3/f/////8w0f///38LowCA/3//vz3pwNb/f/////8w0f///38LowCA/7///3nuea7/v/+/gsU9nf9//79Z6aTW////fwujAID///9/////vwAA/38AgP///3//v/7//7//f////v//v/8//78lyhLl/7///3vkfKT/v///////v/+//7//v/+f/3//v3nuhdH/P/+/JsoS5f+///9b5Fuk/3//////MNH/P/+/JcoS5f9//7+95EHb/3//////MNH/v///ee55rv8//78lyhLl/3//v+bkGNv/f/+/////v/+//7//v/+f/3//v////7//f///////v/+//78RuPaj/7////7//7//f////v//v/+//78Kr3mo/z//v0DJn+T/v///e+R7pP+////+//+//7//v7+un6j/f/+/ee6F0Q==")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_4y7sm")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_mniiw"]
data = PackedVector3Array(1, 0.025, -1, -1, -0.1, -1, 1, -0.1, -1, 1, 0.025, -1, -1, 0.025, -1, -1, -0.1, -1, -1, 0.025, 1, -1, -0.1, 1, -1, 0.025, 0.5, 1, -0.1, -1, 1, 0.025, -0.5, 1, 0.025, -1, -0.025, 0.05, 0.525, -1, 0.05, 0.975, -1, 0.05, 0.525, -0.025, 0.05, 0.525, -0.025, 0.05, 0.975, -1, 0.05, 0.975, -1, 0.025, -1, -1, 0.025, -0.5, -1, -0.1, -1, 0.975, 0.05, -0.975, -0.975, 0.05, -0.525, -0.975, 0.05, -0.975, 0.975, 0.05, -0.975, 0.975, 0.05, -0.525, -0.975, 0.05, -0.525, -0.025, 0.05, -0.475, -1, 0.05, -0.025, -1, 0.05, -0.475, -0.025, 0.05, -0.475, -0.025, 0.05, -0.025, -1, 0.05, -0.025, 1, 0.05, -0.475, 0.025, 0.05, -0.025, 0.025, 0.05, -0.475, 1, 0.05, -0.475, 1, 0.05, -0.025, 0.025, 0.05, -0.025, 1, 0.05, 0.525, 0.025, 0.05, 0.975, 0.025, 0.05, 0.525, 1, 0.05, 0.525, 1, 0.05, 0.975, 0.025, 0.05, 0.975, 0.975, 0.05, 0.025, -0.975, 0.05, 0.475, -0.975, 0.05, 0.025, 0.975, 0.05, 0.025, 0.975, 0.05, 0.475, -0.975, 0.05, 0.475, -0.975, 0.05, -0.525, -1, 0.025, -1, -0.975, 0.05, -0.975, -0.975, 0.05, -0.525, -1, 0.025, -0.5, -1, 0.025, -1, -0.975, 0.05, -0.975, 1, 0.025, -1, 0.975, 0.05, -0.975, -0.975, 0.05, -0.975, -1, 0.025, -1, 1, 0.025, -1, -0.975, 0.05, 0.475, -1, 0.025, 0, -0.975, 0.05, 0.025, -0.975, 0.05, 0.475, -1, 0.025, 0.5, -1, 0.025, 0, 0.975, 0.05, -0.975, 1, 0.025, -0.5, 0.975, 0.05, -0.525, 0.975, 0.05, -0.975, 1, 0.025, -1, 1, 0.025, -0.5, 0.975, 0.05, 0.025, 1, 0.025, 0.5, 0.975, 0.05, 0.475, 0.975, 0.05, 0.025, 1, 0.025, 0, 1, 0.025, 0.5, 0, 0.025, -0.5, 0.975, 0.05, -0.525, 1, 0.025, -0.5, 0.975, 0.05, 0.025, 0, 0.025, 0, 1, 0.025, 0, 0, 0.025, 0.5, 0.975, 0.05, 0.475, 1, 0.025, 0.5, -0.025, 0.05, -0.475, 0, 0.025, 0, -0.025, 0.05, -0.025, -0.025, 0.05, -0.475, 0, 0.025, -0.5, 0, 0.025, 0, 0, 0.025, 0.5, 0.025, 0.05, 0.975, 0, 0.025, 1, 0, 0.025, 0.5, 0.025, 0.05, 0.525, 0.025, 0.05, 0.975, 1, 0.05, 0.975, 0, 0.025, 1, 0.025, 0.05, 0.975, 1, 0.05, 0.975, 1, 0.025, 1, 0, 0.025, 1, 0.025, 0.05, 0.525, 1, 0.025, 0.5, 1, 0.05, 0.525, 0.025, 0.05, 0.525, 0, 0.025, 0.5, 1, 0.025, 0.5, 1, 0.05, 0.525, 1, 0.025, 1, 1, 0.05, 0.975, 1, 0.05, 0.525, 1, 0.025, 0.5, 1, 0.025, 1, -1, 0.025, 0, -0.025, 0.05, -0.025, 0, 0.025, 0, -1, 0.025, 0, -1, 0.05, -0.025, -0.025, 0.05, -0.025, -0.025, 0.05, 0.975, -1, 0.025, 1, -1, 0.05, 0.975, -0.025, 0.05, 0.975, 0, 0.025, 1, -1, 0.025, 1, -1, 0.05, 0.975, -1, 0.025, 0.5, -1, 0.05, 0.525, -1, 0.05, 0.975, -1, 0.025, 1, -1, 0.025, 0.5, 1, 0.05, -0.475, 1, 0.025, 0, 1, 0.05, -0.025, 1, 0.05, -0.475, 1, 0.025, -0.5, 1, 0.025, 0, 0.025, 0.05, -0.475, 1, 0.025, -0.5, 1, 0.05, -0.475, 0.025, 0.05, -0.475, 0, 0.025, -0.5, 1, 0.025, -0.5, 0, 0.025, 0, 1, 0.05, -0.025, 1, 0.025, 0, 0, 0.025, 0, 0.025, 0.05, -0.025, 1, 0.05, -0.025, -1, 0.05, -0.475, 0, 0.025, -0.5, -0.025, 0.05, -0.475, -1, 0.05, -0.475, -1, 0.025, -0.5, 0, 0.025, -0.5, 0, 0.025, -0.5, 0.025, 0.05, -0.025, 0, 0.025, 0, 0, 0.025, -0.5, 0.025, 0.05, -0.475, 0.025, 0.05, -0.025, -1, 0.05, -0.025, -1, 0.025, -0.5, -1, 0.05, -0.475, -1, 0.05, -0.025, -1, 0.025, 0, -1, 0.025, -0.5, -1, 0.05, 0.525, 0, 0.025, 0.5, -0.025, 0.05, 0.525, -1, 0.05, 0.525, -1, 0.025, 0.5, 0, 0.025, 0.5, -0.025, 0.05, 0.525, 0, 0.025, 1, -0.025, 0.05, 0.975, -0.025, 0.05, 0.525, 0, 0.025, 0.5, 0, 0.025, 1, 0, 0.025, 0.5, -0.975, 0.05, 0.475, 0.975, 0.05, 0.475, -1, 0.025, 0.5, -0.975, 0.05, 0.475, 0, 0.025, 0.5, 0, 0.025, -0.5, -0.975, 0.05, -0.525, 0.975, 0.05, -0.525, -1, 0.025, -0.5, -0.975, 0.05, -0.525, 0, 0.025, -0.5, -0.975, 0.05, 0.025, 0, 0.025, 0, 0.975, 0.05, 0.025, -1, 0.025, 0, 0, 0.025, 0, -0.975, 0.05, 0.025, 1, 0.025, 1, 1, -0.1, 1, 0, 0.025, 1, -1, 0.025, 1, 0, 0.025, 1, -1, -0.1, 1, -1, -0.1, 1, 0, 0.025, 1, 1, -0.1, 1, -1, 0.025, 0.5, -1, -0.1, 1, -1, 0.025, 0, -1, -0.1, -1, -1, 0.025, -0.5, -1, 0.025, 0, -1, -0.1, 1, -1, -0.1, -1, -1, 0.025, 0, 1, -0.1, 1, 1, 0.025, 0.5, 1, 0.025, 0, 1, 0.025, 1, 1, 0.025, 0.5, 1, -0.1, 1, 1, -0.1, -1, 1, 0.025, 0, 1, 0.025, -0.5, 1, -0.1, 1, 1, 0.025, 0, 1, -0.1, -1)

[node name="Builds" type="Node3D"]

[node name="floor_tile_small2" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 1)

[node name="floor_tile_small" type="MeshInstance3D" parent="floor_tile_small2"]
mesh = SubResource("ArrayMesh_y823x")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="floor_tile_small2/floor_tile_small"]

[node name="CollisionShape3D" type="CollisionShape3D" parent="floor_tile_small2/floor_tile_small/StaticBody3D"]
shape = SubResource("ConcavePolygonShape3D_6giyx")

[node name="floor_tile_small_broken_A2" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 1)

[node name="floor_tile_small_broken_A" type="MeshInstance3D" parent="floor_tile_small_broken_A2"]
mesh = SubResource("ArrayMesh_cykgr")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="floor_tile_small_broken_A2/floor_tile_small_broken_A"]

[node name="CollisionShape3D" type="CollisionShape3D" parent="floor_tile_small_broken_A2/floor_tile_small_broken_A/StaticBody3D"]
shape = SubResource("ConcavePolygonShape3D_qf7vx")

[node name="floor_tile_small_broken_B2" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 1)

[node name="floor_tile_small_broken_B" type="MeshInstance3D" parent="floor_tile_small_broken_B2"]
mesh = SubResource("ArrayMesh_4jqne")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="floor_tile_small_broken_B2/floor_tile_small_broken_B"]

[node name="CollisionShape3D" type="CollisionShape3D" parent="floor_tile_small_broken_B2/floor_tile_small_broken_B/StaticBody3D"]
shape = SubResource("ConcavePolygonShape3D_q21ax")

[node name="floor_wood_small2" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 1)

[node name="floor_wood_small" type="MeshInstance3D" parent="floor_wood_small2"]
mesh = SubResource("ArrayMesh_irwer")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="floor_wood_small2/floor_wood_small"]

[node name="CollisionShape3D" type="CollisionShape3D" parent="floor_wood_small2/floor_wood_small/StaticBody3D"]
shape = SubResource("ConcavePolygonShape3D_oywtm")

[node name="floor_wood_small_dark2" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 1)

[node name="floor_wood_small_dark" type="MeshInstance3D" parent="floor_wood_small_dark2"]
mesh = SubResource("ArrayMesh_8trks")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="floor_wood_small_dark2/floor_wood_small_dark"]

[node name="CollisionShape3D" type="CollisionShape3D" parent="floor_wood_small_dark2/floor_wood_small_dark/StaticBody3D"]
shape = SubResource("ConcavePolygonShape3D_mniiw")
