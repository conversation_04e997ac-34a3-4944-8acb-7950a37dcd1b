[gd_scene load_steps=12 format=3 uid="uid://ccqjpbrubh270"]

[ext_resource type="Script" uid="uid://cb42l68i45udk" path="res://view/room/room.gd" id="1_5hvow"]
[ext_resource type="MeshLibrary" uid="uid://thvujxkl1w42" path="res://assets/MeshLibrary/floor.tres" id="2_0oe0i"]
[ext_resource type="Script" uid="uid://ce1dg14h1h6dv" path="res://view/room/room_ui_layer.gd" id="4_g0s57"]
[ext_resource type="MeshLibrary" uid="uid://dy8f5m5k2s6w1" path="res://assets/MeshLibrary/wall.tres" id="4_ndut3"]
[ext_resource type="PackedScene" uid="uid://dql0se3sag8qj" path="res://view/room/minimap/minimap.tscn" id="4_ybr2b"]
[ext_resource type="Theme" uid="uid://cek4udsfjd4md" path="res://assets/theme/cn_font_theme.theme" id="5_qb6gu"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_kyckh"]
sky_horizon_color = Color(0.64625, 0.65575, 0.67075, 1)
ground_horizon_color = Color(0.64625, 0.65575, 0.67075, 1)

[sub_resource type="Sky" id="Sky_7ikse"]
sky_material = SubResource("ProceduralSkyMaterial_kyckh")

[sub_resource type="Environment" id="Environment_n7727"]
background_mode = 2
sky = SubResource("Sky_7ikse")
tonemap_mode = 2
glow_enabled = true

[sub_resource type="InputEventKey" id="InputEventKey_kjrrn"]
pressed = true
keycode = 4194305

[sub_resource type="Shortcut" id="Shortcut_0o601"]
events = [SubResource("InputEventKey_kjrrn")]

[node name="Room" type="Node3D"]
script = ExtResource("1_5hvow")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_n7727")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(-0.866025, -0.433013, 0.25, 0, 0.5, 0.866025, -0.5, 0.75, -0.433013, 0, 0, 0)
shadow_enabled = true

[node name="Floor" type="GridMap" parent="."]
mesh_library = ExtResource("2_0oe0i")
cell_size = Vector3(2, 0.1, 2)
collision_mask = 0
data = {
"cells": PackedInt32Array(0, 0, 0, 1, 0, 0, 2, 0, 0, 3, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 4, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 15, 1, 0, 16, 1, 0, 17, 1, 0, 17, 0, 0, 16, 0, 0, 0, 1, 0, 0, 2, 0, 0, 3, 2, 0, 4, 2, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 1, 14, 0, 2, 14, 0, 3, 14, 0, 4, 14, 0, 5, 14, 2, 6, 14, 0, 7, 14, 0, 8, 14, 0, 9, 14, 0, 10, 14, 0, 11, 14, 0, 12, 14, 0, 13, 14, 0, 14, 14, 0, 15, 14, 0, 16, 14, 0, 16, 13, 0, 16, 12, 4, 16, 11, 4, 16, 10, 4, 16, 9, 4, 16, 8, 4, 16, 7, 0, 16, 6, 0, 16, 5, 0, 16, 4, 0, 16, 3, 0, 16, 2, 4, 17, 2, 0, 17, 3, 0, 17, 4, 0, 17, 5, 0, 17, 6, 0, 17, 7, 0, 17, 8, 0, 17, 14, 0, 17, 13, 0, 17, 12, 0, 17, 11, 0, 17, 10, 0, 17, 9, 0, 15, 2, 4, 14, 1, 2, 13, 1, 0, 12, 1, 0, 11, 1, 0, 10, 1, 0, 9, 1, 0, 8, 1, 4, 7, 1, 4, 6, 1, 4, 5, 1, 0, 4, 1, 0, 3, 1, 0, 2, 1, 0, 1, 1, 0, 1, 2, 2, 2, 2, 2, 3, 2, 0, 4, 2, 2, 5, 2, 2, 6, 2, 0, 7, 2, 0, 8, 2, 0, 9, 2, 0, 10, 2, 0, 11, 2, 0, 12, 2, 0, 13, 2, 0, 14, 2, 4, 15, 3, 4, 14, 3, 2, 13, 3, 0, 12, 3, 0, 11, 3, 1, 10, 3, 1, 9, 3, 1, 8, 3, 0, 7, 3, 0, 6, 3, 0, 5, 3, 2, 4, 3, 0, 3, 3, 0, 2, 3, 0, 1, 3, 2, 1, 4, 2, 2, 4, 4, 3, 4, 0, 4, 4, 2, 5, 4, 2, 6, 4, 1, 7, 4, 1, 8, 4, 1, 9, 4, 0, 10, 4, 0, 11, 4, 0, 12, 4, 0, 13, 4, 0, 14, 4, 2, 15, 4, 4, 15, 5, 0, 14, 5, 3, 13, 5, 3, 12, 5, 0, 11, 5, 0, 10, 5, 2, 9, 5, 0, 8, 5, 4, 7, 5, 4, 6, 5, 1, 5, 5, 2, 4, 5, 0, 3, 5, 2, 2, 5, 0, 1, 5, 0, 1, 6, 2, 2, 6, 0, 3, 6, 2, 4, 6, 0, 5, 6, 2, 6, 6, 1, 7, 6, 0, 8, 6, 4, 9, 6, 2, 10, 6, 2, 11, 6, 0, 12, 6, 0, 13, 6, 3, 14, 6, 3, 15, 6, 1, 15, 7, 1, 14, 7, 3, 13, 7, 3, 12, 7, 0, 11, 7, 0, 10, 7, 4, 9, 7, 4, 8, 7, 4, 7, 7, 2, 6, 7, 1, 2, 7, 0, 1, 7, 2, 4, 7, 1, 5, 7, 1, 3, 7, 1, 2, 8, 2, 1, 8, 2, 1, 9, 2, 1, 10, 0, 1, 11, 0, 1, 12, 0, 1, 13, 0, 2, 13, 2, 3, 13, 2, 4, 13, 3, 5, 13, 3, 6, 13, 3, 7, 13, 0, 8, 13, 3, 9, 13, 3, 10, 13, 1, 11, 13, 0, 12, 13, 0, 13, 13, 3, 14, 13, 3, 15, 13, 0, 15, 12, 4, 15, 11, 0, 15, 9, 0, 15, 8, 0, 15, 10, 0, 14, 8, 1, 13, 8, 0, 12, 8, 0, 11, 8, 0, 10, 8, 4, 9, 8, 0, 8, 8, 0, 7, 8, 0, 6, 8, 4, 5, 8, 0, 4, 8, 0, 3, 8, 0, 2, 9, 2, 2, 10, 2, 2, 11, 2, 2, 12, 0, 3, 12, 2, 4, 12, 2, 5, 12, 2, 6, 12, 3, 7, 12, 3, 8, 12, 3, 9, 12, 3, 10, 12, 0, 11, 12, 1, 12, 12, 3, 13, 12, 3, 14, 12, 3, 14, 11, 1, 14, 10, 0, 14, 9, 1, 13, 9, 0, 12, 9, 0, 11, 9, 4, 10, 9, 4, 9, 9, 1, 8, 9, 0, 7, 9, 0, 6, 9, 0, 5, 9, 4, 4, 9, 4, 3, 9, 4, 3, 10, 4, 4, 10, 0, 5, 10, 0, 6, 10, 0, 7, 10, 0, 8, 10, 3, 9, 10, 3, 10, 10, 0, 11, 10, 0, 12, 10, 0, 12, 11, 3, 13, 11, 0, 13, 10, 1, 11, 11, 1, 3, 11, 2, 4, 11, 2, 5, 11, 2, 6, 11, 2, 7, 11, 1, 8, 11, 3, 9, 11, 3, 10, 11, 1)
}

[node name="Box" type="GridMap" parent="."]
cell_size = Vector3(2, 0.1, 2)
collision_mask = 0

[node name="Wall" type="GridMap" parent="."]
mesh_library = ExtResource("4_ndut3")
cell_size = Vector3(2, 0.1, 2)
collision_mask = 0
data = {
"cells": PackedInt32Array(10, 9, 0, 0, 14, 0, 1, 14, 0, 2, 14, 0, 3, 14, 0, 4, 14, 0, 5, 14, 0, 6, 14, 0, 7, 14, 0, 8, 14, 0, 9, 14, 0, 10, 14, 0, 11, 14, 0, 12, 14, 0, 13, 14, 0, 14, 14, 0, 15, 14, 0, 16, 14, 0, 17, 14, 0, 17, 13, 0, 17, 12, 0, 17, 11, 0, 17, 10, 0, 17, 9, 0, 17, 8, 0, 17, 7, 0, 17, 6, 0, 17, 5, 0, 17, 4, 0, 17, 3, 0, 17, 2, 0, 17, 1, 0, 17, 0, 0, 16, 0, 0, 15, 0, 0, 14, 0, 0, 13, 0, 0, 12, 0, 0, 11, 0, 0, 10, 0, 0, 9, 0, 0, 8, 0, 0, 7, 0, 0, 6, 0, 0, 5, 0, 0, 4, 0, 0, 3, 0, 0, 2, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 2, 0, 0, 3, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0)
}

[node name="CanvasLayer" type="CanvasLayer" parent="."]
script = ExtResource("4_g0s57")

[node name="VBoxContainer" type="VBoxContainer" parent="CanvasLayer"]
offset_left = 30.0
offset_top = 30.0
offset_right = 70.0
offset_bottom = 70.0
theme = ExtResource("5_qb6gu")
theme_override_constants/separation = 25

[node name="level" type="Label" parent="CanvasLayer/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 30
text = "当前关卡: 001"

[node name="step" type="Label" parent="CanvasLayer/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 30
text = "当前步数: 0"

[node name="time" type="Label" parent="CanvasLayer/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 30
text = "游戏时间: 00:00:00"

[node name="Back" type="Button" parent="CanvasLayer/VBoxContainer"]
custom_minimum_size = Vector2(192, 74)
layout_mode = 2
theme_override_font_sizes/font_size = 32
shortcut = SubResource("Shortcut_0o601")
text = "退出 [ESC]"

[node name="ColorRect" type="ColorRect" parent="CanvasLayer"]
custom_minimum_size = Vector2(0, 64)
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_bottom = -60.0
grow_horizontal = 2
grow_vertical = 0
mouse_filter = 2
theme = ExtResource("5_qb6gu")
color = Color(0, 0, 0, 0.313726)

[node name="Label" type="Label" parent="CanvasLayer/ColorRect"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_font_sizes/font_size = 32
text = "[WSAD 上下左右]  [SPACE 加速] [M 地图]  [BACKSPACE 回退]"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Minimap" parent="CanvasLayer" instance=ExtResource("4_ybr2b")]
anchors_preset = -1
anchor_left = 1.0
anchor_bottom = 0.0
offset_left = -320.0
offset_top = 40.0
offset_right = -40.0
offset_bottom = 200.0
grow_horizontal = 0
grow_vertical = 1

[node name="CenterContainer" type="CenterContainer" parent="CanvasLayer"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="Control" type="Control" parent="CanvasLayer/CenterContainer"]
layout_mode = 2

[node name="Minimap" parent="CanvasLayer/CenterContainer/Control" instance=ExtResource("4_ybr2b")]
layout_mode = 2
anchors_preset = 0
anchor_right = 0.0
anchor_bottom = 0.0
offset_left = -360.0
offset_top = -240.0
offset_right = 0.0
offset_bottom = 0.0
grow_horizontal = 0
grow_vertical = 1
scale = Vector2(2, 2)

[connection signal="pressed" from="CanvasLayer/VBoxContainer/Back" to="." method="_on_back_pressed"]
