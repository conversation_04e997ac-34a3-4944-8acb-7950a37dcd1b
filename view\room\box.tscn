[gd_scene load_steps=8 format=4 uid="uid://c6rcay0pxbp53"]

[ext_resource type="Script" uid="uid://bfdfd0nhmnx1p" path="res://view/room/box.gd" id="1_fxmko"]
[ext_resource type="Texture2D" uid="uid://dg3xeuxadmk8t" path="res://assets/Dungeon/gltf/dungeon_texture.png" id="1_poo1m"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_udisl"]
resource_name = "texture"
albedo_texture = ExtResource("1_poo1m")
roughness = 0.45

[sub_resource type="ArrayMesh" id="ArrayMesh_b5t5i"]
_surfaces = [{
"aabb": AABB(-0.75, 5.96046e-08, -0.75, 1.5, 1.5, 1.5),
"format": 34359742465,
"index_count": 564,
"index_data": PackedByteArray("BgAzAB4AEQA8ACkACwAgAAgACwAjACAAAQAaAAIAAQAZABoABAAxABwADAA1ACQAFQA4AC0AAwBHABsACQAiAAoACQAhACIAEwAoABAAEwArACgADgA3ACYAFwAsABQAFwAvACwACwBDACMAEQAqABIAEQApACoAFQAuABYAFQAtAC4AEwA+ACsAFwA6AC8AAQBFABkAAwAYAAAAAwAbABgACQBAACEASwAVAE4ASwAUABUATAAKAEgATAAJAAoAUQAFAE8AUQAEAAUAVQASAFgAVQARABIAWgAMAFcAWgAPAAwAWQAIAFQAWQALAAgAUgARAFUAUgAQABEAUAADAFMAUAACAAMAOAAuAC0AOAA7AC4AQgAhAEAAQgAiACEAPAAqACkAPAA/ACoARgAZAEUARgAaABkAKAA+AD0AKAArAD4APQA/ADwAPQA+AD8ALAA6ADkALAAvADoAOQA7ADgAOQA6ADsASQAUAEsASQAXABQAXQAPAFoAXQAOAA8AWAATAFwAWAASABMAXAAQAFIAXAATABAAUwAAAFsAUwADAAAATQAHAFYATQAGAAcAVAAJAEwAVAAIAAkAVwANAEoAVwAMAA0AFgA6ABcAFgA7ADoAEgA+ABMAEgA/AD4ADQA3AA4ADQA2ADcAFAA4ABUAFAA5ADgADwA1AAwADwA0ADUABwAxAAQABwAwADEAEAA8ABEAEAA9ADwABQAzAAYABQAyADMAJQA2AA0AJwA0AA8AKgA/ABIAKAA9ABAAHwAwAAcAHQAyAAUALAA5ABQALgA7ABYANwA1ADQANwA2ADUANgAkADUANgAlACQAMQAzADIAMQAwADMAHAAyAB0AHAAxADIAGwBEABgAGwBHAEQARwBFAEQARwBGAEUAIwBBACAAIwBDAEEAQwBAAEEAQwBCAEAAMAAeADMAMAAfAB4AJgA0ACcAJgA3ADQAWwABAF8AWwAAAAEASgAOAF0ASgANAA4ATwAGAE0ATwAFAAYAXwACAFAAXwABAAIAVgAEAFEAVgAHAAQATgAWAF4ATgAVABYASAALAFkASAAKAAsACABAAAkACABBAEAABAAdAAUABAAcAB0AAABFAAEAAABEAEUADgAnAA8ADgAmACcACgBDAAsACgBCAEMABgAfAAcABgAeAB8AAgBHAAMAAgBGAEcADAAlAA0ADAAkACUAIABBAAgAIgBCAAoAGgBGAAIAGABEAAAASABKAEkASwBNAEwATgBQAE8AUQBTAFIAVABWAFUAVwBZAFgAWgBcAFsAXQBfAF4ASgBZAFcASgBIAFkAXABXAFgAXABaAFcAWgBfAF0AWgBbAF8ASQBdAF4ASQBKAF0AXgBQAE4AXgBfAFAATwBTAFEATwBQAFMATgBNAEsATgBPAE0ATABWAFQATABNAFYAUgBbAFwAUgBTAFsAWABUAFUAWABZAFQAVQBRAFIAVQBWAFEASwBIAEkASwBMAEgAXgAXAEkAXgAWABcA"),
"name": "texture",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 96,
"vertex_data": PackedByteArray("AABAvxTbcT48iQM/AABAv57EoT88iQM/AABAv57EoT88iQO/AABAvxTbcT48iQO/PIkDvxTbcT4BAEC/PIkDv57EoT8BAEC/PIkDP57EoT8BAEC/PIkDPxTbcT4BAEC/AQBAPxTbcT48iQO/AQBAP57EoT88iQO/AQBAP57EoT88iQM/AQBAPxTbcT48iQM/PIkDPxTbcT4BAEA/PIkDP57EoT8BAEA/PIkDv57EoT8BAEA/PIkDvxTbcT4BAEA/PIkDvwAAgDM8iQO/PIkDPwAAgDM8iQO/PIkDPwAAgDM8iQM/PIkDvwAAgDM8iQM/PIkDPwAAwD88iQO/PIkDvwAAwD88iQO/PIkDvwAAwD88iQM/PIkDPwAAwD88iQM/GwYRv3aNiz6NcvQ+GwYRv6IcnT+NcvQ+GwYRv6IcnT+NcvS+GwYRv3aNiz6NcvS+jXL0vnaNiz4bBhG/jXL0vqIcnT8bBhG/jXL0PqIcnT8bBhG/jXL0PnaNiz4bBhG/GwYRP3aNiz6NcvS+GwYRP6IcnT+NcvS+GwYRP6IcnT+NcvQ+GwYRP3aNiz6NcvQ+jXL0PnaNiz4bBhE/jXL0PqIcnT8bBhE/jXL0vqIcnT8bBhE/jXL0vnaNiz4bBhE/jXL0vpznOz6NcvS+jXL0PpznOz6NcvS+jXL0PpznOz6NcvQ+jXL0vpznOz6NcvQ+jXL0PgyDqD+NcvS+jXL0vgyDqD+NcvS+jXL0vgyDqD+NcvQ+jXL0PgyDqD+NcvQ+CPciPnaNiz4bBhG/CPcivnaNiz4bBhG/CPcivqIcnT8bBhG/CPciPqIcnT8bBhG/CPcivnaNiz4bBhE/CPciPnaNiz4bBhE/CPciPqIcnT8bBhE/CPcivqIcnT8bBhE/CPcivgyDqD+NcvS+CPciPgyDqD+NcvS+CPciPgyDqD+NcvQ+CPcivgyDqD+NcvQ+CPciPpznOz6NcvS+CPcivpznOz6NcvS+CPcivpznOz6NcvQ+CPciPpznOz6NcvQ+GwYRP8K9aD+NcvS+GwYRPz5CFz+NcvS+GwYRP8K9aD+NcvQ+GwYRPz5CFz+NcvQ+GwYRvz5CFz+NcvQ+GwYRv8K9aD+NcvQ+GwYRv8K9aD+NcvS+GwYRvz5CFz+NcvS+AQBAP2Zmtj/OzCw/zswsPwAAwD/OzCw/zswsP2Zmtj8BAEA/zswsPwAAwD/OzCy/AQBAP2Zmtj/OzCy/zswsP2Zmtj8BAEC/zcwsvwAAwD/OzCy/zcwsv2Zmtj8BAEC/AABAv2Zmtj/OzCy/zcwsv6CZmT0BAEC/zcwsvwAAgDPOzCy/AABAv6CZmT3OzCy/AQBAP6CZmT3OzCy/zswsPwAAgDPOzCy/zswsP6CZmT0BAEC/zswsP6CZmT0BAEA/zswsPwAAgDPOzCw/AQBAP6CZmT3OzCw/zcwsv6CZmT0BAEA/AABAv6CZmT3OzCw/zcwsvwAAgDPOzCw/zcwsv2Zmtj8BAEA/zcwsvwAAwD/OzCw/AABAv2Zmtj/OzCw/")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_ci14p"]
resource_name = "box_large_box_large"
_surfaces = [{
"aabb": AABB(-0.75, 5.96046e-08, -0.75, 1.5, 1.5, 1.5),
"attribute_data": PackedByteArray("sE8OPjz1Ez6wTw4+3HcnPrBPDj54+jo+sE8OPlBevj2wTw4+iGPlPbBPDj5kNAY+sE8OPlBevj2wTw4+iGPlPbBPDj5kNAY+sE8OPjz1Ez6wTw4+3HcnPrBPDj54+jo+jO8bPjz1Ez6M7xs+3HcnPozvGz54+jo+jO8bPlBevj2M7xs+iGPlPYzvGz5kNAY+EDdXPlBevj0QN1c+iGPlPRA3Vz5kNAY+EDdXPjz1Ez4QN1c+3HcnPhA3Vz54+jo+6NZkPjz1Ez7o1mQ+3HcnPujWZD54+jo+6NZkPlBevj3o1mQ+iGPlPejWZD5kNAY+6NZkPlBevj3o1mQ+iGPlPejWZD5kNAY+6NZkPjz1Ez7o1mQ+3HcnPujWZD54+jo+EDdXPjz1Ez4QN1c+3HcnPhA3Vz54+jo+EDdXPlBevj0QN1c+iGPlPRA3Vz5kNAY+jO8bPlBevj2M7xs+iGPlPYzvGz5kNAY+jO8bPjz1Ez6M7xs+3HcnPozvGz54+jo+jO8bPpiXSz6M7xs+NBpfPozvGz40Gl8+EDdXPpiXSz4QN1c+NBpfPhA3Vz40Gl8+EDdXPpiXSz4QN1c+NBpfPhA3Vz40Gl8+jO8bPpiXSz6M7xs+NBpfPozvGz40Gl8+EDdXPlA9bD0QN1c+ICnEPRA3Vz4gKcQ9jO8bPlA9bD2M7xs+ICnEPYzvGz4gKcQ9jO8bPlA9bD2M7xs+ICnEPYzvGz4gKcQ9EDdXPlA9bD0QN1c+ICnEPRA3Vz4gKcQ9XOUYPjxmET5c5Rg+eGs4PvcEAz+EakY+XOUYPoCB6j1c5Rg+YMMIPvcEAz+ww3c9XOUYPoCB6j1c5Rg+YMMIPvcEAz+ww3c9XOUYPjxmET5c5Rg+eGs4PvcEAz+EakY+xAgePjxmET7ECB4+eGs4PomeBD9wIyA+xAgePoCB6j3ECB4+YMMIPomeBD9Aez89IB5VPoCB6j0gHlU+YMMIPufGFT8IcMg9IB5VPjxmET4gHlU+eGs4PufGFT+kfFQ+QEFaPjxmET5AQVo+eGs4PptgFz+EakY+QEFaPoCB6j1AQVo+YMMIPptgFz+ww3c9QEFaPoCB6j1AQVo+YMMIPptgFz+ww3c9QEFaPjxmET5AQVo+eGs4PptgFz+EakY+IB5VPjxmET4gHlU+eGs4PufGFT9wIyA+IB5VPoCB6j0gHlU+YMMIPufGFT9Aez89xAgePoCB6j3ECB4+YMMIPomeBD8IcMg9xAgePjxmET7ECB4+eGs4PomeBD+kfFQ+xAgePuQxUj7ECB4+5DFSPomeBD90OzI+IB5VPuQxUj4gHlU+5DFSPufGFT+wlGY+IB5VPuQxUj4gHlU+5DFSPufGFT+wlGY+xAgePuQxUj7ECB4+5DFSPomeBD90OzI+IB5VPvD53T0gHlU+8PndPefGFT/IP6Q9xAgePvD53T3ECB4+8PndPYmeBD9gNe48xAgePvD53T3ECB4+8PndPYmeBD9gNe48IB5VPvD53T0gHlU+8PndPefGFT/IP6Q9rMFCPjxmET7IDhA/cCMgPsgOED+kfFQ+OGUwPjxmET7KVgo/cCMgPspWCj+kfFQ+OGUwPmTDCD7KVgo/QHs/PcpWCj8IcMg9rMFCPmTDCD7IDhA/QHs/PcgOED8IcMg9OGUwPjxmET7KVgo/cCMgPspWCj+kfFQ+rMFCPjxmET7IDhA/cCMgPsgOED+kfFQ+rMFCPmTDCD7IDhA/QHs/PcgOED8IcMg9OGUwPmTDCD7KVgo/QHs/PcpWCj8IcMg9OGUwPvD53T3KVgo/YDXuPMpWCj/IP6Q9rMFCPvD53T3IDhA/YDXuPMgOED/IP6Q9rMFCPvD53T3IDhA/YDXuPMgOED/IP6Q9OGUwPvD53T3KVgo/YDXuPMpWCj/IP6Q9rMFCPugxUj7IDhA/dDsyPsgOED+wlGY+OGUwPugxUj7KVgo/dDsyPspWCj+wlGY+OGUwPugxUj7KVgo/dDsyPspWCj+wlGY+rMFCPugxUj7IDhA/dDsyPsgOED+wlGY+QEFaPlikCz6bYBc/0JWqPZtgFz9Athc+QEFaPugHIj6bYBc/UErZPZtgFz98EC8+QEFaPlikCz6bYBc/0JWqPZtgFz9Athc+QEFaPugHIj6bYBc/UErZPZtgFz98EC8+XOUYPugHIj73BAM/UErZPfcEAz98EC8+XOUYPlCkCz73BAM/0JWqPfcEAz9Athc+XOUYPlikCz73BAM/0JWqPfcEAz9Athc+XOUYPugHIj73BAM/UErZPfcEAz98EC8+6NZkPiBNMz3o1mQ+cDFFPejWZD4Isac96NZkPjCjsD32rFs+wDIePVDYXD5APWw9XINgPmA9bD3o1mQ+4DIePVDYXD4gTTM9UNhcPgixpz1cg2A+YDFFPVyDYD4oo7A96dZkPgDAKD1Q2Fw+4FCgPFDYXD5gPWw9XINgPkA9bD3o1mQ+wDIePejWZD4gTTM96NZkPmAxRT3o1mQ+CLGnPejWZD4oo7A99axbPuBQoDxQ2Fw+CLGnPVyDYD5wMUU9XINgPjCjsD3o1mQ+AMAoPbBPDj7gMh49PaMSPmA9bD1MThY+4FCgPExOFj5gPWw9sE8OPgDAKD09oxI+cDFFPT2jEj4wo7A9TE4WPgixpz2meRc+wFCgPLBPDj4gTTM9sE8OPnAxRT2wTw4+CLGnPbBPDj4wo7A9PaMSPjRQBz49oxI+cFUuPj6jEj5ckiQ+TE4WPmzOMj6leRc+XJIkPj2jEj5ckiQ+PaMSPpiXSz5MThY+XJIkPkxOFj6Yl0s+sE8OPjRQBz6wTw4+XJIkPrBPDj5wVS4+sE8OPmzOMj7o1mQ+NFAHPujWZD4syQs+6NZkPnBVLj7o1mQ+aM4yPlDYXD5ckiQ+UNhcPpiXSz5cg2A+mJdLPujWZD68DxE+9qxbPlySJD5Q2Fw+bM4yPlyDYD40UAc+XINgPnBVLj7p1mQ+eGwOPvWsWz5ckiQ+UNhcPmjOMj5cg2A+NFAHPlyDYD5wVS4+6NZkPjTJCz5Q2Fw+XJIkPlDYXD6Yl0s+XINgPpiXSz7o1mQ+vA8RPujWZD40UAc+6NZkPjDJCz7o1mQ+cFUuPujWZD5szjI+PaMSPjRQBz49oxI+cFUuPj6jEj5ckiQ+TE4WPmzOMj6meRc+XJIkPrBPDj40UAc+sE8OPlySJD6wTw4+cFUuPrBPDj5ozjI+PaMSPlySJD49oxI+mJdLPkxOFj5ckiQ+TE4WPpiXSz6wTw4+ME0zPT2jEj5wMUU9PaMSPjCjsD1MThY+IE0zPUxOFj4Isac9sE8OPsAyHj09oxI+YD1sPUxOFj5gPWw9pnkXPvAyHj2wTw4+IE0zPbBPDj5gMUU9sE8OPgixpz2wTw4+KKOwPQ=="),
"format": ***********,
"index_count": 564,
"index_data": PackedByteArray("FACZAFsANQC0AHwAIQBgABgAIQBpAGAABQBPAAgABQBMAE8ADACTAFQAJACfAGwAQACoAIcACwDVAFIAHQBnACAAHQBkAGcAOwB5ADIAOwCCAHkALAClAHMARgCEAD0ARgCNAIQAIwDJAGoANAB+ADcANAB7AH4AQQCLAEQAQQCIAIsAOgC6AIEARwCuAI4ABADPAEsACQBIAAAACQBRAEgAHADAAGMA5gA/APUA5gA8AD8A6wAeANoA6wAbAB4AAAEPAPgAAAENAA8AEgE2ACABEgEzADYAKQElABoBKQEuACUAJQEZAA8BJQEiABkABwEzABEBBwEwADMA/gAKAAoB/gAGAAoAqgCMAIkAqgCzAIwAyABlAMIAyABoAGUAtQCAAH0AtQC+AIAA1ABNANEA1ABQAE0AegC8ALkAegCDALwAuAC/ALYAuAC7AL8AhgCvAKwAhgCPAK8ArQCyAKkArQCwALIA3gA8AOcA3gBFADwANQEuACcBNQEqAC4AHwE5ADIBHwE2ADkAMAEwAAUBMAE5ADAACwEBAC4BCwEKAAEA8AAWABcB8AASABYADgEbAOwADgEZABsAHAEnAOMAHAElACcAQwCuAEcAQwCxAK4AOAC6ADoAOAC9ALoAKQClACwAKQCiAKUAPgCoAEAAPgCrAKgALQCfACQALQCcAJ8AFQCTAAwAFQCQAJMAMQC0ADUAMQC3ALQAEQCZABQAEQCWAJkAcACiACkAdQCcAC0AfwC9ADgAeAC3ADEAXQCQABUAWACWABEAhQCrAD4AigCxAEMApwCgAJ4ApwCjAKAApABuAKEApABxAG4AlACbAJcAlACSAJsAVgCYAFkAVgCVAJgAUwDNAEoAUwDWAM0A1wDQAM4A1wDTANAAawDEAGIAawDKAMQAywDBAMUAywDHAMEAkQBcAJoAkQBfAFwAdACdAHcAdACmAJ0ALQEDAD8BLQEBAAMA4QAqADcB4QAnACoA+QASAO4A+QAPABIAPgEGAP0APgEDAAYAFQENAAIBFQEWAA0A8wBCADkB8wA/AEIA2wAiACQB2wAeACIAGgDAABwAGgDDAMAADgBXABAADgBVAFcAAgDPAAQAAgDMAM8AKwB2AC8AKwByAHYAHwDJACMAHwDGAMkAEwBeABcAEwBaAF4ABwDVAAsABwDSANUAJgBvACgAJgBtAG8AYQDDABoAZgDGAB8ATgDSAAcASQDMAAIA2ADkAN8A6ADxAOkA8gD7APYAAQEJAQQBDQEYARMBHQEjASEBKAEvASwBMwE8ATgB4gAiARsB4gDZACIBMQEZAR4BMQEqARkBJgE9ATQBJgErAT0B3AA2ATsB3ADgADYBOAH7APIAOAE8AfsA9wAIAf8A9wD8AAgB9ADtAOUA9AD6AO0A6gAWAQwB6gDvABYBBAEsAS8BBAEJASwBIQENARMBIQEjAQ0BEAEDAQYBEAEUAQMB6ADYAN8A6ADpANgAOgFFAN0AOgFCAEUA"),
"material": SubResource("StandardMaterial3D_udisl"),
"name": "texture",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 320,
"vertex_data": PackedByteArray("AABAvxTbcT48iQM/AABAvxTbcT48iQM/AABAvxTbcT48iQM/AABAv57EoT88iQM/AABAv57EoT88iQM/AABAv57EoT88iQM/AABAv57EoT88iQO/AABAv57EoT88iQO/AABAv57EoT88iQO/AABAvxTbcT48iQO/AABAvxTbcT48iQO/AABAvxTbcT48iQO/PIkDvxTbcT4BAEC/PIkDvxTbcT4BAEC/PIkDvxTbcT4BAEC/PIkDv57EoT8BAEC/PIkDv57EoT8BAEC/PIkDv57EoT8BAEC/PIkDP57EoT8BAEC/PIkDP57EoT8BAEC/PIkDP57EoT8BAEC/PIkDPxTbcT4BAEC/PIkDPxTbcT4BAEC/PIkDPxTbcT4BAEC/AQBAPxTbcT48iQO/AQBAPxTbcT48iQO/AQBAPxTbcT48iQO/AQBAP57EoT88iQO/AQBAP57EoT88iQO/AQBAP57EoT88iQO/AQBAP57EoT88iQM/AQBAP57EoT88iQM/AQBAP57EoT88iQM/AQBAPxTbcT48iQM/AQBAPxTbcT48iQM/AQBAPxTbcT48iQM/PIkDPxTbcT4BAEA/PIkDPxTbcT4BAEA/PIkDPxTbcT4BAEA/PIkDP57EoT8BAEA/PIkDP57EoT8BAEA/PIkDP57EoT8BAEA/PIkDv57EoT8BAEA/PIkDv57EoT8BAEA/PIkDv57EoT8BAEA/PIkDvxTbcT4BAEA/PIkDvxTbcT4BAEA/PIkDvxTbcT4BAEA/PIkDvwAAgDM8iQO/PIkDvwAAgDM8iQO/PIkDvwAAgDM8iQO/PIkDPwAAgDM8iQO/PIkDPwAAgDM8iQO/PIkDPwAAgDM8iQO/PIkDPwAAgDM8iQM/PIkDPwAAgDM8iQM/PIkDPwAAgDM8iQM/PIkDvwAAgDM8iQM/PIkDvwAAgDM8iQM/PIkDvwAAgDM8iQM/PIkDPwAAwD88iQO/PIkDPwAAwD88iQO/PIkDPwAAwD88iQO/PIkDvwAAwD88iQO/PIkDvwAAwD88iQO/PIkDvwAAwD88iQO/PIkDvwAAwD88iQM/PIkDvwAAwD88iQM/PIkDvwAAwD88iQM/PIkDPwAAwD88iQM/PIkDPwAAwD88iQM/PIkDPwAAwD88iQM/GwYRv3aNiz6NcvQ+GwYRv3aNiz6NcvQ+GwYRv3aNiz6NcvQ+GwYRv6IcnT+NcvQ+GwYRv6IcnT+NcvQ+GwYRv6IcnT+NcvQ+GwYRv6IcnT+NcvS+GwYRv6IcnT+NcvS+GwYRv6IcnT+NcvS+GwYRv3aNiz6NcvS+GwYRv3aNiz6NcvS+GwYRv3aNiz6NcvS+jXL0vnaNiz4bBhG/jXL0vnaNiz4bBhG/jXL0vnaNiz4bBhG/jXL0vqIcnT8bBhG/jXL0vqIcnT8bBhG/jXL0vqIcnT8bBhG/jXL0PqIcnT8bBhG/jXL0PqIcnT8bBhG/jXL0PqIcnT8bBhG/jXL0PnaNiz4bBhG/jXL0PnaNiz4bBhG/jXL0PnaNiz4bBhG/GwYRP3aNiz6NcvS+GwYRP3aNiz6NcvS+GwYRP3aNiz6NcvS+GwYRP6IcnT+NcvS+GwYRP6IcnT+NcvS+GwYRP6IcnT+NcvS+GwYRP6IcnT+NcvQ+GwYRP6IcnT+NcvQ+GwYRP6IcnT+NcvQ+GwYRP3aNiz6NcvQ+GwYRP3aNiz6NcvQ+GwYRP3aNiz6NcvQ+jXL0PnaNiz4bBhE/jXL0PnaNiz4bBhE/jXL0PnaNiz4bBhE/jXL0PqIcnT8bBhE/jXL0PqIcnT8bBhE/jXL0PqIcnT8bBhE/jXL0vqIcnT8bBhE/jXL0vqIcnT8bBhE/jXL0vqIcnT8bBhE/jXL0vnaNiz4bBhE/jXL0vnaNiz4bBhE/jXL0vnaNiz4bBhE/jXL0vpznOz6NcvS+jXL0vpznOz6NcvS+jXL0vpznOz6NcvS+jXL0PpznOz6NcvS+jXL0PpznOz6NcvS+jXL0PpznOz6NcvS+jXL0PpznOz6NcvQ+jXL0PpznOz6NcvQ+jXL0PpznOz6NcvQ+jXL0vpznOz6NcvQ+jXL0vpznOz6NcvQ+jXL0vpznOz6NcvQ+jXL0PgyDqD+NcvS+jXL0PgyDqD+NcvS+jXL0PgyDqD+NcvS+jXL0vgyDqD+NcvS+jXL0vgyDqD+NcvS+jXL0vgyDqD+NcvS+jXL0vgyDqD+NcvQ+jXL0vgyDqD+NcvQ+jXL0vgyDqD+NcvQ+jXL0PgyDqD+NcvQ+jXL0PgyDqD+NcvQ+jXL0PgyDqD+NcvQ+CPciPnaNiz4bBhG/CPciPnaNiz4bBhG/CPciPnaNiz4bBhG/CPcivnaNiz4bBhG/CPcivnaNiz4bBhG/CPcivnaNiz4bBhG/CPcivqIcnT8bBhG/CPcivqIcnT8bBhG/CPcivqIcnT8bBhG/CPciPqIcnT8bBhG/CPciPqIcnT8bBhG/CPciPqIcnT8bBhG/CPcivnaNiz4bBhE/CPcivnaNiz4bBhE/CPcivnaNiz4bBhE/CPciPnaNiz4bBhE/CPciPnaNiz4bBhE/CPciPnaNiz4bBhE/CPciPqIcnT8bBhE/CPciPqIcnT8bBhE/CPciPqIcnT8bBhE/CPcivqIcnT8bBhE/CPcivqIcnT8bBhE/CPcivqIcnT8bBhE/CPcivgyDqD+NcvS+CPcivgyDqD+NcvS+CPcivgyDqD+NcvS+CPciPgyDqD+NcvS+CPciPgyDqD+NcvS+CPciPgyDqD+NcvS+CPciPgyDqD+NcvQ+CPciPgyDqD+NcvQ+CPciPgyDqD+NcvQ+CPcivgyDqD+NcvQ+CPcivgyDqD+NcvQ+CPcivgyDqD+NcvQ+CPciPpznOz6NcvS+CPciPpznOz6NcvS+CPciPpznOz6NcvS+CPcivpznOz6NcvS+CPcivpznOz6NcvS+CPcivpznOz6NcvS+CPcivpznOz6NcvQ+CPcivpznOz6NcvQ+CPcivpznOz6NcvQ+CPciPpznOz6NcvQ+CPciPpznOz6NcvQ+CPciPpznOz6NcvQ+GwYRP8K9aD+NcvS+GwYRP8K9aD+NcvS+GwYRP8K9aD+NcvS+GwYRPz5CFz+NcvS+GwYRPz5CFz+NcvS+GwYRPz5CFz+NcvS+GwYRP8K9aD+NcvQ+GwYRP8K9aD+NcvQ+GwYRP8K9aD+NcvQ+GwYRPz5CFz+NcvQ+GwYRPz5CFz+NcvQ+GwYRPz5CFz+NcvQ+GwYRvz5CFz+NcvQ+GwYRvz5CFz+NcvQ+GwYRvz5CFz+NcvQ+GwYRv8K9aD+NcvQ+GwYRv8K9aD+NcvQ+GwYRv8K9aD+NcvQ+GwYRv8K9aD+NcvS+GwYRv8K9aD+NcvS+GwYRv8K9aD+NcvS+GwYRvz5CFz+NcvS+GwYRvz5CFz+NcvS+GwYRvz5CFz+NcvS+AQBAP2Zmtj/OzCw/AQBAP2Zmtj/OzCw/AQBAP2Zmtj/OzCw/AQBAP2Zmtj/OzCw/zswsPwAAwD/OzCw/zswsPwAAwD/OzCw/zswsPwAAwD/OzCw/zswsPwAAwD/OzCw/zswsP2Zmtj8BAEA/zswsP2Zmtj8BAEA/zswsP2Zmtj8BAEA/zswsP2Zmtj8BAEA/zswsP2Zmtj8BAEA/zswsPwAAwD/OzCy/zswsPwAAwD/OzCy/zswsPwAAwD/OzCy/zswsPwAAwD/OzCy/AQBAP2Zmtj/OzCy/AQBAP2Zmtj/OzCy/AQBAP2Zmtj/OzCy/AQBAP2Zmtj/OzCy/zswsP2Zmtj8BAEC/zswsP2Zmtj8BAEC/zswsP2Zmtj8BAEC/zswsP2Zmtj8BAEC/zswsP2Zmtj8BAEC/zcwsvwAAwD/OzCy/zcwsvwAAwD/OzCy/zcwsvwAAwD/OzCy/zcwsvwAAwD/OzCy/zcwsv2Zmtj8BAEC/zcwsv2Zmtj8BAEC/zcwsv2Zmtj8BAEC/zcwsv2Zmtj8BAEC/zcwsv2Zmtj8BAEC/AABAv2Zmtj/OzCy/AABAv2Zmtj/OzCy/AABAv2Zmtj/OzCy/AABAv2Zmtj/OzCy/zcwsv6CZmT0BAEC/zcwsv6CZmT0BAEC/zcwsv6CZmT0BAEC/zcwsv6CZmT0BAEC/zcwsv6CZmT0BAEC/zcwsvwAAgDPOzCy/zcwsvwAAgDPOzCy/zcwsvwAAgDPOzCy/zcwsvwAAgDPOzCy/AABAv6CZmT3OzCy/AABAv6CZmT3OzCy/AABAv6CZmT3OzCy/AABAv6CZmT3OzCy/AQBAP6CZmT3OzCy/AQBAP6CZmT3OzCy/AQBAP6CZmT3OzCy/AQBAP6CZmT3OzCy/zswsPwAAgDPOzCy/zswsPwAAgDPOzCy/zswsPwAAgDPOzCy/zswsPwAAgDPOzCy/zswsP6CZmT0BAEC/zswsP6CZmT0BAEC/zswsP6CZmT0BAEC/zswsP6CZmT0BAEC/zswsP6CZmT0BAEC/zswsP6CZmT0BAEA/zswsP6CZmT0BAEA/zswsP6CZmT0BAEA/zswsP6CZmT0BAEA/zswsP6CZmT0BAEA/zswsPwAAgDPOzCw/zswsPwAAgDPOzCw/zswsPwAAgDPOzCw/zswsPwAAgDPOzCw/AQBAP6CZmT3OzCw/AQBAP6CZmT3OzCw/AQBAP6CZmT3OzCw/AQBAP6CZmT3OzCw/zcwsv6CZmT0BAEA/zcwsv6CZmT0BAEA/zcwsv6CZmT0BAEA/zcwsv6CZmT0BAEA/zcwsv6CZmT0BAEA/AABAv6CZmT3OzCw/AABAv6CZmT3OzCw/AABAv6CZmT3OzCw/AABAv6CZmT3OzCw/zcwsvwAAgDPOzCw/zcwsvwAAgDPOzCw/zcwsvwAAgDPOzCw/zcwsvwAAgDPOzCw/zcwsv2Zmtj8BAEA/zcwsv2Zmtj8BAEA/zcwsv2Zmtj8BAEA/zcwsv2Zmtj8BAEA/zcwsv2Zmtj8BAEA/zcwsvwAAwD/OzCw/zcwsvwAAwD/OzCw/zcwsvwAAwD/OzCw/zcwsvwAAwD/OzCw/AABAv2Zmtj/OzCw/AABAv2Zmtj/OzCw/AABAv2Zmtj/OzCw/AABAv2Zmtj/OzCw/0mrR6v///78AAP9/////PwAA0er+/2k1AAD/f////z8AANHq/v+WStJqLRX///+/AAD/f////7/Sav9/0er/v9JqLRX///+/0mrR6v///78AAP9/////v9Jq/3/R6v+/LJX//////z//////////P///LJUslf8//////////z///yyVLJX/PyyVAAD+//8//////////z8AACyV/v+WCiyVAAD+//8/LJX//////z//////////PwAALJX+/2l1LJXR6v///7////9/////vyyV/3/+/2i1////f////78slf9//v+VyiyVLRX///+/////f////7///9Hq0er/PyyVLRX///+/LJXR6v///7////9/////v///0erR6v8//3/R6v///7//f/9/ydvkrS0V/38slf+//3//f////78tFf9/LJX/v/9/LRX+//+//3//f////7/R6v9//v+Viv9/LRX+//+//3/R6v///7//f/9/KeNpztHq/3/+/2j1/38AAP///7//f9Jq/v//v9Hq0mr///+//38AAP///78tFdJq////v/9/0mr+//+//38AAP///78tFdJq////v9HqAAD+//8//38AAP///7/R6gAA/v//P9Hq0mr///+//3/+/5Ce/78tFSyV////v/9/LJX///+//3/+/////7//fyyV////v9HqLJX///+//3/+//7//7/R6v//////P9HqLJX///+//3/+//7//78tFSyV////v9Hq//////8/0mrR6v///78AANHq/v9pNQAA/3////+/AADR6v7/lkrSai0V////vwAA/3////+/0mr/f9Hq/r/Sai0V////vwAA/3////+/0mrR6v///7/Sav9/0er/vwAA/3////+/LJX//////z///yyVLJX/P/////9bta0a//8slSyV/z8slQAA/v//P/////9bta0aAAAslf7/lgoslQAA/v//P/////9bta0aLJX//////z8AACyV/v9odf////9bta0aLJXR6v///78slf9//v9otf///3////+/LJX/f/7/lcoslS0V////v////3////+////R6tHq/z8slS0V////v////3////+/LJXR6v///7///9Hq0er/P////3////+//3/R6v///78tFf9/LJX/v/9//39bta2aLRX/fyyV/7//fy0V/v/+v/9//39bta2a0er/f/7/lYr/fy0V/v//v/9//39bta2a/3/R6v///7/R6v9//v9o9f9//39bta2a/3/Sav7//7/R6tJq////v/9/AAD///+/LRXSav///7//f9Jq/v//v/9/AAD///+/LRXSav///7/R6gAA/v//P/9/AAD///+/0eoAAP7//z/R6tJq////v/9/AAD///+/LRUslf///7//fyyV////v/9///////+//38slf///7/R6iyV////v/9///////+/0er//////z/R6iyV////v/9///////+/LRUslf///7/R6v//////P/9///////+/LJX//////z//////W7WtGv////9bta0aLJX//////z//////W7WtGv////9bta0aLJUAAP7//z//////W7WtGv////9bta0aLJUAAP7//z//////W7WtGv////9bta0a/3/R6v///7//f/9/W7Wtmv9//39bta2a/3/R6v///7//f/9/W7Wtmv9//39bta2a/38tFf7//7//f/9/W7Wtmv9//39bta2a/38tFf7//7//f/9/W7Wtmv9//39bta2a/38slf///7//f///////v/9///////+//38slf///7//f///////v/9///////+/0er//////z//f///////v/9///////+/0er//////z//f///////v/9///////+//3/Sav7//7//fwAA////v/9/AAD///+//3/Sav7//7//fwAA////v/9/AAD///+/0eoAAP7//z//fwAA////v/9/AAD///+/0eoAAP7//z//fwAA////v/9/AAD///+/LJX/f/7/lcr///9/////v////3////+/LJX/f/7/aLX///9/////v////3////+////R6tHq/z////9/////v////3////+////R6tHq/z////9/////v////3////+/AADR6v7/aDUAAP9/////vwAA/3////+/AADR6v7/lkoAAP9/////vwAA/3////+/0mr/f9Hq/78AAP9/////vwAA/3////+/0mr/f9Hq/78AAP9/////vwAA/3////+/5ccMnLRsz07lxwycsPP1keXHDJz///8/5ccMnP///z8MnOXHh+KDqwyc5cez7j1ZDJzlx6qOCa4MnOXHX2KlSQycDJx/9IOrDJwMnH/0g6sMnAycsPMc2AycDJwb8ObZDJwMnPVRBFcYuPLj////vxi48uMH17tOGLjy4y6hpcgYuPLj////v/LjGLj///+/8uMYuOujJkby4xi4////v/LjGLj///+/8uPy41jUMLfy4/LjB9e/RfLj8uPEz9g58uPy43jPsjny4/Lj////v+ZH8uP///+/5kfy4////7/mR/Lj9uXS1eZH8uOH4oQrDBzy4////78MHPLjsPMcWAwc8uMb8OdZDBzy43/0hCsMHPLj3voV0wwcGLj///+/DBwYuLDz9REMHBi4////vwwcGLj///+/DBwMHLDz4icMHAwcZPO8JwwcDBz///+/DBwMHH/0e1QMHAwc////v+ZHDBz///+/5kcMHP///7/mRwwc////v+ZHDBz///+/DBzmR7DzCW4MHOZH////vwwc5kf///+/DBzmR////7/y4+ZH66PYOfLj5kegHVn28uPmR////7/y4+ZH////vxi4DBz///+/GLgMHP///78YuAwc////vxi4DBxLEy/x8uMMHP///7/y4wwcB9dAOvLjDBzEzyZG8uMMHC/M8Ufy4wwcCi766Ayc8mP///8/DJzyY3/0etQMnPJjsPPipwyc8mNk87unDJzyY////z8MnBk4////PwycGTj///8/DJwZOP///z8MnBk4////P+XH8mOw8wnu5cfyY////z/lx/Jj////P+XH8mP///8/8mPyY8TPJsbyY/JjL8zwx/Jj8mP///8/8mPyYwfXP7ryY/Jj////Pxk48mPro9i5GTjyY////z8ZOPJj////Pxk48mP///8/8mMZOP///z/yYxk4////P/JjGTj///8/8mMZOP///z/yYwyc////P/JjDJzEz9i58mMMnHjPsbnyYwycB9e+xfJjDJwH177F8mPlx////z/yY+XH////P/Jj5ccH17vO8mPlxwfXu84ZOAyc////Pxk4DJzroybGGTgMnP///z8ZOAyc////Pw==")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_b5t5i")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_u5725"]
data = PackedVector3Array(0.5138, 1.2638, -0.75, 0.1591, 1.2274, -0.5665, 0.4774, 1.2274, -0.5665, 0.5138, 0, -0.5138, 0.1591, 0.1835, -0.4774, 0.4774, 0.1835, -0.4774, 0.75, 0.2362, 0.5138, 0.5665, 0.2726, -0.4774, 0.75, 0.2362, -0.5138, 0.75, 0.2362, 0.5138, 0.5665, 0.2726, 0.4774, 0.5665, 0.2726, -0.4774, -0.75, 1.2638, 0.5138, -0.5665, 1.2274, -0.4774, -0.75, 1.2638, -0.5138, -0.75, 1.2638, 0.5138, -0.5665, 1.2274, 0.4774, -0.5665, 1.2274, -0.4774, -0.5138, 0.2362, -0.75, -0.1591, 0.2726, -0.5665, -0.4774, 0.2726, -0.5665, 0.5138, 0.2362, 0.75, 0.1591, 0.2726, 0.5665, 0.4774, 0.2726, 0.5665, -0.5138, 1.5, -0.5138, -0.1591, 1.3165, -0.4774, -0.4774, 1.3165, -0.4774, -0.75, 0.2362, -0.5138, -0.5665, 0.5909, -0.4774, -0.5665, 0.2726, -0.4774, 0.75, 1.2638, -0.5138, 0.5665, 1.2274, 0.4774, 0.75, 1.2638, 0.5138, 0.75, 1.2638, -0.5138, 0.5665, 1.2274, -0.4774, 0.5665, 1.2274, 0.4774, -0.5138, 0, 0.5138, -0.4774, 0.1835, -0.4774, -0.5138, 0, -0.5138, -0.5138, 0, 0.5138, -0.4774, 0.1835, 0.4774, -0.4774, 0.1835, -0.4774, -0.5138, 1.2638, 0.75, -0.1591, 1.2274, 0.5665, -0.4774, 1.2274, 0.5665, 0.5138, 1.5, 0.5138, 0.4774, 1.3165, -0.4774, 0.5138, 1.5, -0.5138, 0.5138, 1.5, 0.5138, 0.4774, 1.3165, 0.4774, 0.4774, 1.3165, -0.4774, 0.75, 0.2362, 0.5138, 0.5665, 0.5909, 0.4774, 0.5665, 0.2726, 0.4774, 0.5138, 0, -0.5138, 0.4774, 0.1835, 0.4774, 0.5138, 0, 0.5138, 0.5138, 0, -0.5138, 0.4774, 0.1835, -0.4774, 0.4774, 0.1835, 0.4774, -0.5138, 1.5, -0.5138, -0.4774, 1.3165, 0.4774, -0.5138, 1.5, 0.5138, -0.5138, 1.5, -0.5138, -0.4774, 1.3165, -0.4774, -0.4774, 1.3165, 0.4774, -0.5138, 0, 0.5138, -0.1591, 0.1835, 0.4774, -0.4774, 0.1835, 0.4774, 0.5138, 1.5, 0.5138, 0.1591, 1.3165, 0.4774, 0.4774, 1.3165, 0.4774, -0.75, 1.2638, 0.5138, -0.5665, 0.9091, 0.4774, -0.5665, 1.2274, 0.4774, -0.75, 0.2362, -0.5138, -0.5665, 0.2726, 0.4774, -0.75, 0.2362, 0.5138, -0.75, 0.2362, -0.5138, -0.5665, 0.2726, -0.4774, -0.5665, 0.2726, 0.4774, 0.75, 1.2638, -0.5138, 0.5665, 0.9091, -0.4774, 0.5665, 1.2274, -0.4774, 0.675, 1.5, -0.675, -0.5138, 1.5, -0.5138, -0.675, 1.5, -0.675, 0.675, 1.5, -0.675, 0.5138, 1.5, -0.5138, -0.5138, 1.5, -0.5138, 0.75, 1.425, -0.675, 0.75, 1.2638, 0.5138, 0.75, 1.425, 0.675, 0.75, 1.425, -0.675, 0.75, 1.2638, -0.5138, 0.75, 1.2638, 0.5138, -0.675, 0.075, -0.75, -0.5138, 1.2638, -0.75, -0.675, 1.425, -0.75, -0.675, 0.075, -0.75, -0.5138, 0.2362, -0.75, -0.5138, 1.2638, -0.75, 0.675, 0, -0.675, 0.5138, 0, 0.5138, 0.675, 0, 0.675, 0.675, 0, -0.675, 0.5138, 0, -0.5138, 0.5138, 0, 0.5138, -0.675, 0.075, 0.75, 0.5138, 0.2362, 0.75, 0.675, 0.075, 0.75, -0.675, 0.075, 0.75, -0.5138, 0.2362, 0.75, 0.5138, 0.2362, 0.75, 0.75, 0.075, 0.675, 0.75, 0.2362, -0.5138, 0.75, 0.075, -0.675, 0.75, 0.075, 0.675, 0.75, 0.2362, 0.5138, 0.75, 0.2362, -0.5138, -0.675, 0, -0.675, 0.5138, 0, -0.5138, 0.675, 0, -0.675, -0.675, 0, -0.675, -0.5138, 0, -0.5138, 0.5138, 0, -0.5138, -0.75, 1.425, -0.675, -0.75, 0.2362, -0.5138, -0.75, 0.075, -0.675, -0.75, 1.425, -0.675, -0.75, 1.2638, -0.5138, -0.75, 0.2362, -0.5138, -0.1591, 1.3165, -0.4774, -0.4774, 1.3165, 0.4774, -0.4774, 1.3165, -0.4774, -0.1591, 1.3165, -0.4774, -0.1591, 1.3165, 0.4774, -0.4774, 1.3165, 0.4774, 0.5665, 0.9091, 0.4774, 0.5665, 1.2274, -0.4774, 0.5665, 0.9091, -0.4774, 0.5665, 0.9091, 0.4774, 0.5665, 1.2274, 0.4774, 0.5665, 1.2274, -0.4774, 0.1591, 0.1835, -0.4774, 0.4774, 0.1835, 0.4774, 0.4774, 0.1835, -0.4774, 0.1591, 0.1835, -0.4774, 0.1591, 0.1835, 0.4774, 0.4774, 0.1835, 0.4774, -0.5665, 0.9091, -0.4774, -0.5665, 1.2274, 0.4774, -0.5665, 0.9091, 0.4774, -0.5665, 0.9091, -0.4774, -0.5665, 1.2274, -0.4774, -0.5665, 1.2274, 0.4774, -0.4774, 0.1835, -0.4774, -0.1591, 0.1835, 0.4774, -0.1591, 0.1835, -0.4774, -0.4774, 0.1835, -0.4774, -0.4774, 0.1835, 0.4774, -0.1591, 0.1835, 0.4774, -0.1591, 0.1835, -0.4774, 0.1591, 0.1835, 0.4774, 0.1591, 0.1835, -0.4774, -0.1591, 0.1835, -0.4774, -0.1591, 0.1835, 0.4774, 0.1591, 0.1835, 0.4774, 0.4774, 1.3165, -0.4774, 0.1591, 1.3165, 0.4774, 0.1591, 1.3165, -0.4774, 0.4774, 1.3165, -0.4774, 0.4774, 1.3165, 0.4774, 0.1591, 1.3165, 0.4774, 0.1591, 1.3165, -0.4774, -0.1591, 1.3165, 0.4774, -0.1591, 1.3165, -0.4774, 0.1591, 1.3165, -0.4774, 0.1591, 1.3165, 0.4774, -0.1591, 1.3165, 0.4774, 0.675, 1.5, 0.675, 0.5138, 1.5, -0.5138, 0.675, 1.5, -0.675, 0.675, 1.5, 0.675, 0.5138, 1.5, 0.5138, 0.5138, 1.5, -0.5138, -0.675, 1.425, 0.75, -0.5138, 0.2362, 0.75, -0.675, 0.075, 0.75, -0.675, 1.425, 0.75, -0.5138, 1.2638, 0.75, -0.5138, 0.2362, 0.75, 0.675, 0, 0.675, -0.5138, 0, 0.5138, -0.675, 0, 0.675, 0.675, 0, 0.675, 0.5138, 0, 0.5138, -0.5138, 0, 0.5138, -0.675, 0, 0.675, -0.5138, 0, -0.5138, -0.675, 0, -0.675, -0.675, 0, 0.675, -0.5138, 0, 0.5138, -0.5138, 0, -0.5138, -0.75, 0.075, -0.675, -0.75, 0.2362, 0.5138, -0.75, 0.075, 0.675, -0.75, 0.075, -0.675, -0.75, 0.2362, -0.5138, -0.75, 0.2362, 0.5138, 0.675, 1.425, -0.75, 0.5138, 0.2362, -0.75, 0.675, 0.075, -0.75, 0.675, 1.425, -0.75, 0.5138, 1.2638, -0.75, 0.5138, 0.2362, -0.75, 0.75, 0.075, -0.675, 0.75, 1.2638, -0.5138, 0.75, 1.425, -0.675, 0.75, 0.075, -0.675, 0.75, 0.2362, -0.5138, 0.75, 1.2638, -0.5138, 0.675, 0.075, 0.75, 0.5138, 1.2638, 0.75, 0.675, 1.425, 0.75, 0.675, 0.075, 0.75, 0.5138, 0.2362, 0.75, 0.5138, 1.2638, 0.75, -0.5138, 1.5, 0.5138, 0.1591, 1.3165, 0.4774, 0.5138, 1.5, 0.5138, -0.5138, 1.5, 0.5138, -0.1591, 1.3165, 0.4774, 0.1591, 1.3165, 0.4774, 0.5138, 0, 0.5138, -0.1591, 0.1835, 0.4774, -0.5138, 0, 0.5138, 0.5138, 0, 0.5138, 0.1591, 0.1835, 0.4774, -0.1591, 0.1835, 0.4774, 0.5138, 1.2638, 0.75, -0.1591, 1.2274, 0.5665, -0.5138, 1.2638, 0.75, 0.5138, 1.2638, 0.75, 0.1591, 1.2274, 0.5665, -0.1591, 1.2274, 0.5665, 0.5138, 1.5, -0.5138, -0.1591, 1.3165, -0.4774, -0.5138, 1.5, -0.5138, 0.5138, 1.5, -0.5138, 0.1591, 1.3165, -0.4774, -0.1591, 1.3165, -0.4774, -0.5138, 0.2362, 0.75, 0.1591, 0.2726, 0.5665, 0.5138, 0.2362, 0.75, -0.5138, 0.2362, 0.75, -0.1591, 0.2726, 0.5665, 0.1591, 0.2726, 0.5665, 0.5138, 0.2362, -0.75, -0.1591, 0.2726, -0.5665, -0.5138, 0.2362, -0.75, 0.5138, 0.2362, -0.75, 0.1591, 0.2726, -0.5665, -0.1591, 0.2726, -0.5665, -0.5138, 0, -0.5138, 0.1591, 0.1835, -0.4774, 0.5138, 0, -0.5138, -0.5138, 0, -0.5138, -0.1591, 0.1835, -0.4774, 0.1591, 0.1835, -0.4774, -0.5138, 1.2638, -0.75, 0.1591, 1.2274, -0.5665, 0.5138, 1.2638, -0.75, -0.5138, 1.2638, -0.75, -0.1591, 1.2274, -0.5665, 0.1591, 1.2274, -0.5665, 0.4774, 1.2274, 0.5665, 0.1591, 1.2274, 0.5665, 0.5138, 1.2638, 0.75, -0.4774, 0.2726, 0.5665, -0.1591, 0.2726, 0.5665, -0.5138, 0.2362, 0.75, 0.4774, 0.1835, 0.4774, 0.1591, 0.1835, 0.4774, 0.5138, 0, 0.5138, -0.4774, 0.1835, -0.4774, -0.1591, 0.1835, -0.4774, -0.5138, 0, -0.5138, 0.4774, 0.2726, -0.5665, 0.1591, 0.2726, -0.5665, 0.5138, 0.2362, -0.75, -0.4774, 1.2274, -0.5665, -0.1591, 1.2274, -0.5665, -0.5138, 1.2638, -0.75, 0.4774, 1.3165, -0.4774, 0.1591, 1.3165, -0.4774, 0.5138, 1.5, -0.5138, -0.4774, 1.3165, 0.4774, -0.1591, 1.3165, 0.4774, -0.5138, 1.5, 0.5138, -0.1591, 1.2274, 0.5665, 0.1591, 0.2726, 0.5665, -0.1591, 0.2726, 0.5665, -0.1591, 1.2274, 0.5665, 0.1591, 1.2274, 0.5665, 0.1591, 0.2726, 0.5665, 0.1591, 1.2274, 0.5665, 0.4774, 0.2726, 0.5665, 0.1591, 0.2726, 0.5665, 0.1591, 1.2274, 0.5665, 0.4774, 1.2274, 0.5665, 0.4774, 0.2726, 0.5665, -0.1591, 0.2726, -0.5665, 0.1591, 1.2274, -0.5665, -0.1591, 1.2274, -0.5665, -0.1591, 0.2726, -0.5665, 0.1591, 0.2726, -0.5665, 0.1591, 1.2274, -0.5665, -0.4774, 0.2726, -0.5665, -0.1591, 1.2274, -0.5665, -0.4774, 1.2274, -0.5665, -0.4774, 0.2726, -0.5665, -0.1591, 0.2726, -0.5665, -0.1591, 1.2274, -0.5665, -0.5665, 0.2726, -0.4774, -0.5665, 0.5909, 0.4774, -0.5665, 0.2726, 0.4774, -0.5665, 0.2726, -0.4774, -0.5665, 0.5909, -0.4774, -0.5665, 0.5909, 0.4774, -0.5665, 0.5909, -0.4774, -0.5665, 0.9091, 0.4774, -0.5665, 0.5909, 0.4774, -0.5665, 0.5909, -0.4774, -0.5665, 0.9091, -0.4774, -0.5665, 0.9091, 0.4774, 0.5665, 0.2726, 0.4774, 0.5665, 0.5909, -0.4774, 0.5665, 0.2726, -0.4774, 0.5665, 0.2726, 0.4774, 0.5665, 0.5909, 0.4774, 0.5665, 0.5909, -0.4774, 0.5665, 0.5909, 0.4774, 0.5665, 0.9091, -0.4774, 0.5665, 0.5909, -0.4774, 0.5665, 0.5909, 0.4774, 0.5665, 0.9091, 0.4774, 0.5665, 0.9091, -0.4774, 0.1591, 0.2726, -0.5665, 0.4774, 1.2274, -0.5665, 0.1591, 1.2274, -0.5665, 0.1591, 0.2726, -0.5665, 0.4774, 0.2726, -0.5665, 0.4774, 1.2274, -0.5665, -0.4774, 1.2274, 0.5665, -0.1591, 0.2726, 0.5665, -0.4774, 0.2726, 0.5665, -0.4774, 1.2274, 0.5665, -0.1591, 1.2274, 0.5665, -0.1591, 0.2726, 0.5665, -0.75, 0.075, 0.675, -0.75, 1.2638, 0.5138, -0.75, 1.425, 0.675, -0.75, 0.075, 0.675, -0.75, 0.2362, 0.5138, -0.75, 1.2638, 0.5138, 0.675, 1.425, 0.75, -0.5138, 1.2638, 0.75, -0.675, 1.425, 0.75, 0.675, 1.425, 0.75, 0.5138, 1.2638, 0.75, -0.5138, 1.2638, 0.75, -0.675, 1.425, -0.75, 0.5138, 1.2638, -0.75, 0.675, 1.425, -0.75, -0.675, 1.425, -0.75, -0.5138, 1.2638, -0.75, 0.5138, 1.2638, -0.75, -0.75, 1.425, 0.675, -0.75, 1.2638, -0.5138, -0.75, 1.425, -0.675, -0.75, 1.425, 0.675, -0.75, 1.2638, 0.5138, -0.75, 1.2638, -0.5138, 0.675, 0.075, -0.75, -0.5138, 0.2362, -0.75, -0.675, 0.075, -0.75, 0.675, 0.075, -0.75, 0.5138, 0.2362, -0.75, -0.5138, 0.2362, -0.75, -0.675, 1.5, -0.675, -0.5138, 1.5, 0.5138, -0.675, 1.5, 0.675, -0.675, 1.5, -0.675, -0.5138, 1.5, -0.5138, -0.5138, 1.5, 0.5138, 0.75, 1.425, 0.675, 0.75, 0.2362, 0.5138, 0.75, 0.075, 0.675, 0.75, 1.425, 0.675, 0.75, 1.2638, 0.5138, 0.75, 0.2362, 0.5138, 0.75, 0.2362, -0.5138, 0.5665, 0.9091, -0.4774, 0.75, 1.2638, -0.5138, 0.75, 0.2362, -0.5138, 0.5665, 0.5909, -0.4774, 0.5665, 0.9091, -0.4774, -0.5138, 0.2362, -0.75, -0.4774, 1.2274, -0.5665, -0.5138, 1.2638, -0.75, -0.5138, 0.2362, -0.75, -0.4774, 0.2726, -0.5665, -0.4774, 1.2274, -0.5665, -0.75, 0.2362, 0.5138, -0.5665, 0.9091, 0.4774, -0.75, 1.2638, 0.5138, -0.75, 0.2362, 0.5138, -0.5665, 0.5909, 0.4774, -0.5665, 0.9091, 0.4774, -0.5138, 1.2638, 0.75, -0.4774, 0.2726, 0.5665, -0.5138, 0.2362, 0.75, -0.5138, 1.2638, 0.75, -0.4774, 1.2274, 0.5665, -0.4774, 0.2726, 0.5665, 0.75, 1.2638, 0.5138, 0.5665, 0.5909, 0.4774, 0.75, 0.2362, 0.5138, 0.75, 1.2638, 0.5138, 0.5665, 0.9091, 0.4774, 0.5665, 0.5909, 0.4774, 0.5138, 1.2638, -0.75, 0.4774, 0.2726, -0.5665, 0.5138, 0.2362, -0.75, 0.5138, 1.2638, -0.75, 0.4774, 1.2274, -0.5665, 0.4774, 0.2726, -0.5665, -0.75, 1.2638, -0.5138, -0.5665, 0.5909, -0.4774, -0.75, 0.2362, -0.5138, -0.75, 1.2638, -0.5138, -0.5665, 0.9091, -0.4774, -0.5665, 0.5909, -0.4774, 0.5138, 0.2362, 0.75, 0.4774, 1.2274, 0.5665, 0.5138, 1.2638, 0.75, 0.5138, 0.2362, 0.75, 0.4774, 0.2726, 0.5665, 0.4774, 1.2274, 0.5665, 0.5665, 0.2726, -0.4774, 0.5665, 0.5909, -0.4774, 0.75, 0.2362, -0.5138, 0.5665, 1.2274, 0.4774, 0.5665, 0.9091, 0.4774, 0.75, 1.2638, 0.5138, -0.5665, 1.2274, -0.4774, -0.5665, 0.9091, -0.4774, -0.75, 1.2638, -0.5138, -0.5665, 0.2726, 0.4774, -0.5665, 0.5909, 0.4774, -0.75, 0.2362, 0.5138, 0.75, 1.425, 0.675, 0.675, 1.425, 0.75, 0.675, 1.5, 0.675, 0.675, 1.5, -0.675, 0.675, 1.425, -0.75, 0.75, 1.425, -0.675, -0.675, 1.5, -0.675, -0.75, 1.425, -0.675, -0.675, 1.425, -0.75, -0.675, 0.075, -0.75, -0.75, 0.075, -0.675, -0.675, 0, -0.675, 0.75, 0.075, -0.675, 0.675, 0.075, -0.75, 0.675, 0, -0.675, 0.675, 0.075, 0.75, 0.75, 0.075, 0.675, 0.675, 0, 0.675, -0.675, 0.075, 0.75, -0.675, 0, 0.675, -0.75, 0.075, 0.675, -0.675, 1.425, 0.75, -0.75, 1.425, 0.675, -0.675, 1.5, 0.675, 0.675, 1.425, 0.75, 0.75, 0.075, 0.675, 0.675, 0.075, 0.75, 0.675, 1.425, 0.75, 0.75, 1.425, 0.675, 0.75, 0.075, 0.675, -0.675, 0, 0.675, 0.675, 0.075, 0.75, 0.675, 0, 0.675, -0.675, 0, 0.675, -0.675, 0.075, 0.75, 0.675, 0.075, 0.75, -0.675, 0.075, 0.75, -0.75, 1.425, 0.675, -0.675, 1.425, 0.75, -0.675, 0.075, 0.75, -0.75, 0.075, 0.675, -0.75, 1.425, 0.675, 0.675, 1.5, 0.675, -0.675, 1.425, 0.75, -0.675, 1.5, 0.675, 0.675, 1.5, 0.675, 0.675, 1.425, 0.75, -0.675, 1.425, 0.75, -0.675, 1.5, 0.675, -0.75, 1.425, -0.675, -0.675, 1.5, -0.675, -0.675, 1.5, 0.675, -0.75, 1.425, 0.675, -0.75, 1.425, -0.675, -0.675, 1.425, -0.75, -0.75, 0.075, -0.675, -0.675, 0.075, -0.75, -0.675, 1.425, -0.75, -0.75, 1.425, -0.675, -0.75, 0.075, -0.675, -0.675, 1.5, -0.675, 0.675, 1.425, -0.75, 0.675, 1.5, -0.675, -0.675, 1.5, -0.675, -0.675, 1.425, -0.75, 0.675, 1.425, -0.75, 0.75, 1.425, -0.675, 0.675, 0.075, -0.75, 0.75, 0.075, -0.675, 0.75, 1.425, -0.675, 0.675, 1.425, -0.75, 0.675, 0.075, -0.75, -0.675, 0, -0.675, -0.75, 0.075, 0.675, -0.675, 0, 0.675, -0.675, 0, -0.675, -0.75, 0.075, -0.675, -0.75, 0.075, 0.675, 0.675, 0, 0.675, 0.75, 0.075, -0.675, 0.675, 0, -0.675, 0.675, 0, 0.675, 0.75, 0.075, 0.675, 0.75, 0.075, -0.675, 0.675, 0, -0.675, -0.675, 0.075, -0.75, -0.675, 0, -0.675, 0.675, 0, -0.675, 0.675, 0.075, -0.75, -0.675, 0.075, -0.75, 0.675, 1.5, -0.675, 0.75, 1.425, 0.675, 0.675, 1.5, 0.675, 0.675, 1.5, -0.675, 0.75, 1.425, -0.675, 0.75, 1.425, 0.675, -0.675, 1.5, 0.675, 0.5138, 1.5, 0.5138, 0.675, 1.5, 0.675, -0.675, 1.5, 0.675, -0.5138, 1.5, 0.5138, 0.5138, 1.5, 0.5138)

[sub_resource type="BoxShape3D" id="BoxShape3D_dvu7f"]
size = Vector3(2, 1.6, 2)

[node name="Box" type="Node3D"]
script = ExtResource("1_fxmko")

[node name="box_large2" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 1)

[node name="box_large" type="MeshInstance3D" parent="box_large2"]
mesh = SubResource("ArrayMesh_ci14p")
skeleton = NodePath("")

[node name="StaticBody3D" type="StaticBody3D" parent="box_large2/box_large"]
collision_mask = 0

[node name="CollisionShape3D" type="CollisionShape3D" parent="box_large2/box_large/StaticBody3D"]
shape = SubResource("ConcavePolygonShape3D_u5725")

[node name="Area3D" type="Area3D" parent="."]
collision_layer = 0
collision_mask = 2

[node name="CollisionShape3D" type="CollisionShape3D" parent="Area3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0.8, 1)
shape = SubResource("BoxShape3D_dvu7f")

[connection signal="body_entered" from="Area3D" to="." method="_on_player_entered"]
[connection signal="body_exited" from="Area3D" to="." method="_on_player_exited"]
